// File: STM32G4xx_DGBMCU.ini
// Version: 1.0.0
// Note: refer to STM32G4xx Reference manual (RM0440)
//       refer to STM32G4xx datasheets


/* variable to hold register values */
define unsigned long DbgMCU_CR;
define unsigned long DbgMCU_APB1_Fz1;
define unsigned long DbgMCU_APB1_Fz2;
define unsigned long DbgMCU_APB2_Fz;

// <<< Use Configuration Wizard in Context Menu >>>

// <h> Debug MCU configuration register (DBGMCU_CR)
//                                   <i> Reserved bits must be kept at reset value
//   <o.6..7> TRACE_MODE             <i> Trace Mode
//      <0=> Asynchronous
//      <1=> Synchronous: TRACEDATA Size 1
//      <2=> Synchronous: TRACEDATA Size 2
//      <3=> Synchronous: TRACEDATA Size 4
//   <o.5>  TRACE_IOEN               <i> Trace I/O Enable
//   <o.2>  DBG_STANDBY              <i> Debug Standby mode
//   <o.1>  DBG_STOP                 <i> Debug Stop mode
//   <o.0>  DBG_SLEEP                <i> Debug Sleep mode
// </h>
DbgMCU_CR = 0x00000007;

// <h> Debug MCU APB1 freeze register1 (DBGMCU_APB1FZR1)
//                                   <i> Reserved bits must be kept at reset value
//   <o.31> DBG_LPTIM1_STOP          <i> LPTIM1 counter stopped when core is halted
//   <o.30> DBG_I2C3_STOP            <i> I2C3 SMBUS timeout counter stopped when core is halted
//   <o.22> DBG_I2C2_STOP            <i> I2C2 SMBUS timeout counter stopped when core is halted
//   <o.21> DBG_I2C1_STOP            <i> I2C1 SMBUS timeout counter stopped when core is halted
//   <o.12> DBG_IWDG_STOP            <i> Independent watchdog counter stopped when core is halted
//   <o.11> DBG_WWDG_STOP            <i> Window watchdog counter stopped when core is halted
//   <o.10> DBG_RTC_STOP             <i> RTC counter stopped when core is halted
//   <o.5>  DBG_TIM7_STOP            <i> TIM7 counter stopped when core is halted
//   <o.4>  DBG_TIM6_STOP            <i> TIM6 counter stopped when core is halted
//   <o.3>  DBG_TIM5_STOP            <i> TIM5 counter stopped when core is halted
//   <o.2>  DBG_TIM4_STOP            <i> TIM4 counter stopped when core is halted
//   <o.1>  DBG_TIM3_STOP            <i> TIM3 counter stopped when core is halted
//   <o.0>  DBG_TIM2_STOP            <i> TIM2 counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz1 = 0x00000000;

// <h> Debug MCU APB1 freeze register 2 (DBGMCU_APB1FZR2)
//                                   <i> Reserved bits must be kept at reset value
//   <o.1>  DBG_I2C4_STOP            <i> I2C4 SMBUS timeout counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz2 = 0x00000000;

// <h> Debug MCU APB2 freeze register (DBGMCU_APB2FZR)
//                                   <i> Reserved bits must be kept at reset value
//   <o.26> DBG_HRTIM_STOP           <i> HRTIM counter stopped when core is halted
//   <o.20> DBG_TIM20_STOP           <i> TIM20 counter stopped when core is halted
//   <o.18> DBG_TIM17_STOP           <i> TIM17 counter stopped when core is halted
//   <o.17> DBG_TIM16_STOP           <i> TIM16 counter stopped when core is halted
//   <o.16> DBG_TIM15_STOP           <i> TIM15 counter stopped when core is halted
//   <o.13> DBG_TIM8_STOP            <i> TIM8 counter stopped when core is halted
//   <o.11> DBG_TIM1_STOP            <i> TIM1 counter stopped when core is halted
// </h>
DbgMCU_APB2_Fz = 0x00000000;

// <h> TPIU Pin Routing
//   <i> TRACECLK: Pin PE2
//   <i> TRACED[0]: Pin PE3
//   <i> TRACED[1]: Pin PE4
//   <i> TRACED[2]: Pin PE5
//   <i> TRACED[3]: Pin PE6
// </h>

// <<< end of configuration section >>>

/*----------------------------------------------------------------------------
  Setup_TracePins()  configure the used trace pins
 *----------------------------------------------------------------------------*/
FUNC void Setup_TracePins (unsigned char trace_mode) {

  if (trace_mode == 0) {           /* asynchronous mode */
          /* configure SWO (PB3) */
          _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000002) );  // RCC_AHB2ENR: IO port B clock enable
          _WDWORD(0x48000400, ((_RDWORD(0x48000400) & ~0x000000C0) | 0x00000080) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48000408, ((_RDWORD(0x48000408)              ) | 0x000000C0) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800040C, ((_RDWORD(0x4800040C) & ~0x000000C0)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48000420, ((_RDWORD(0x48000420) & ~0x0000F000)             ) );  // GPIOx_AFRL:    Alternate Function to AF0
  }
  else {                           /* synchronous mode */
          /* configure TRACECLK (PE2) */
          _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000010) );  // RCC_AHB2ENR: IO port E clock enable
          _WDWORD(0x48001000, ((_RDWORD(0x48001000) & ~0x00000030) | 0x00000020) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48001008, ((_RDWORD(0x48001008)              ) | 0x00000030) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800100C, ((_RDWORD(0x4800100C) & ~0x00000030)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48001020, ((_RDWORD(0x48001020) & ~0x00000F00)             ) );  // GPIOx_AFRL:    Alternate Function to AF0

    switch (trace_mode) {
      case 3:                      /* TRACEDATA[3..2] */
        /* configure TRACED3 */
          /* configure TRACED3 (PE6) */
       // _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000010) );  // RCC_AHB2ENR: IO port E clock enable
          _WDWORD(0x48001000, ((_RDWORD(0x48001000) & ~0x00003000) | 0x00002000) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48001008, ((_RDWORD(0x48001008)              ) | 0x00003000) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800100C, ((_RDWORD(0x4800100C) & ~0x00003000)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48001020, ((_RDWORD(0x48001020) & ~0x0F000000)             ) );  // GPIOx_AFRL:    Alternate Function to AF0

        /* configure TRACED2 */
          /* configure TRACED2 (PE5) */
       // _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000010) );  // RCC_AHB2ENR: IO port E clock enable
          _WDWORD(0x48001000, ((_RDWORD(0x48001000) & ~0x00000C00) | 0x00000800) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48001008, ((_RDWORD(0x48001008)              ) | 0x00000C00) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800100C, ((_RDWORD(0x4800100C) & ~0x00000C00)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48001020, ((_RDWORD(0x48001020) & ~0x00F00000)             ) );  // GPIOx_AFRL:    Alternate Function to AF0

      case 2:                      /* TRACEDATA[1] */
        /* configure TRACED1 */
          /* configure TRACED1 (PE4) */
       // _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000010) );  // RCC_AHB2ENR: IO port E clock enable
          _WDWORD(0x48001000, ((_RDWORD(0x48001000) & ~0x00000300) | 0x00000200) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48001008, ((_RDWORD(0x48001008)              ) | 0x00000300) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800100C, ((_RDWORD(0x4800100C) & ~0x00000300)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48001020, ((_RDWORD(0x48001020) & ~0x000F0000)             ) );  // GPIOx_AFRL:    Alternate Function to AF0

      case 1:                      /* TRACEDATA[0] */
        /* configure TRACED0 */
          /* configure TRACED0 (PE3) */
       // _WDWORD(0x4002104C, ( _RDWORD(0x4002104C)                | 0x00000010) );  // RCC_AHB2ENR: IO port E clock enable
          _WDWORD(0x48001000, ((_RDWORD(0x48001000) & ~0x000000C0) | 0x00000080) );  // GPIOx_MODER:   Set Mode (Alternate Function)
          _WDWORD(0x48001008, ((_RDWORD(0x48001008)              ) | 0x000000C0) );  // GPIOx_OSPEEDR: Set Speed (Very High Speed)
          _WDWORD(0x4800100C, ((_RDWORD(0x4800100C) & ~0x000000C0)             ) );  // GPIOx_PUPDR:   Set I/O to no pull-up/pull-down
          _WDWORD(0x48001020, ((_RDWORD(0x48001020) & ~0x0000F000)             ) );  // GPIOx_AFRL:    Alternate Function to AF0
        break;
    }
  }

}

/*----------------------------------------------------------------------------
  Setup_DBGMCU()  configure DBGMCU registers
 *----------------------------------------------------------------------------*/
FUNC void Setup_DBGMCU (void) {

  if (DbgMCU_CR & (1 << 5)){
    Setup_TracePins (((DbgMCU_CR >> 6) & 3));
  }

  _WDWORD(0xE0042004, DbgMCU_CR);                                // Set DBGMCU_CR
  _WDWORD(0xE0042008, DbgMCU_APB1_Fz1);                          // Set DBGMCU_APB1FZR1
  _WDWORD(0xE004200C, DbgMCU_APB1_Fz2);                          // Set DBGMCU_APB1FZR2
  _WDWORD(0xE0042010, DbgMCU_APB2_Fz);                           // Set DBGMCU_APB2FZR
}


/*----------------------------------------------------------------------------
  OnResetExec()  Executed after reset via uVision's 'Reset'-button
 *----------------------------------------------------------------------------*/
FUNC void OnResetExec (void) {
  Setup_DBGMCU();
}

Setup_DBGMCU();                                           // Debugger Setup
