#MicroXplorer Configuration settings - do not modify
ADC1.Channel-7\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.IPParameters=Rank-7\#ChannelRegularConversion,Channel-7\#ChannelRegularConversion,SamplingTime-7\#ChannelRegularConversion,OffsetNumber-7\#ChannelRegularConversion,NbrOfConversionFlag,master
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-7\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Rank-7\#ChannelRegularConversion=1
ADC1.SamplingTime-7\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.master=1
ADC2.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_6
ADC2.IPParameters=Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversionFlag
ADC2.NbrOfConversionFlag=1
ADC2.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.Rank-2\#ChannelRegularConversion=1
ADC2.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
DAC1.DAC_Channel-DAC_OUT2_ExtAndInt=DAC_CHANNEL_2
DAC1.IPParameters=DAC_Channel-DAC_OUT2_ExtAndInt
File.Version=6
I2C3.IPParameters=Timing,Timeout
I2C3.Timeout=0x0000881B
I2C3.Timing=0x30A0A7FB
KeepUserPlacement=false
Mcu.Family=STM32G4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=RCC
Mcu.IP11=SAI1
Mcu.IP12=SPI2
Mcu.IP13=SYS
Mcu.IP14=TIM8
Mcu.IP15=USART1
Mcu.IP16=USART3
Mcu.IP17=USB
Mcu.IP2=COMP6
Mcu.IP3=DAC1
Mcu.IP4=FDCAN1
Mcu.IP5=FDCAN2
Mcu.IP6=FMC
Mcu.IP7=HRTIM1
Mcu.IP8=I2C3
Mcu.IP9=NVIC
Mcu.IPNb=18
Mcu.Name=STM32G474Q(B-C-E)Tx
Mcu.Package=LQFP128
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PF5
Mcu.Pin11=PF7
Mcu.Pin12=PF8
Mcu.Pin13=PF9
Mcu.Pin14=PF10
Mcu.Pin15=PF0-OSC_IN
Mcu.Pin16=PF1-OSC_OUT
Mcu.Pin17=PC0
Mcu.Pin18=PF2
Mcu.Pin19=PA0
Mcu.Pin2=PE4
Mcu.Pin20=PA1
Mcu.Pin21=PA4
Mcu.Pin22=PA5
Mcu.Pin23=PB2
Mcu.Pin24=PF11
Mcu.Pin25=PF12
Mcu.Pin26=PF13
Mcu.Pin27=PF14
Mcu.Pin28=PF15
Mcu.Pin29=PE7
Mcu.Pin3=PE5
Mcu.Pin30=PE8
Mcu.Pin31=PE9
Mcu.Pin32=PE10
Mcu.Pin33=PE11
Mcu.Pin34=PE12
Mcu.Pin35=PE13
Mcu.Pin36=PE14
Mcu.Pin37=PE15
Mcu.Pin38=PB10
Mcu.Pin39=PB11
Mcu.Pin4=PE6
Mcu.Pin40=PB12
Mcu.Pin41=PB13
Mcu.Pin42=PB14
Mcu.Pin43=PB15
Mcu.Pin44=PD8
Mcu.Pin45=PD9
Mcu.Pin46=PD10
Mcu.Pin47=PD11
Mcu.Pin48=PD12
Mcu.Pin49=PD13
Mcu.Pin5=PC13
Mcu.Pin50=PD14
Mcu.Pin51=PD15
Mcu.Pin52=PC6
Mcu.Pin53=PC7
Mcu.Pin54=PG0
Mcu.Pin55=PG1
Mcu.Pin56=PG2
Mcu.Pin57=PG3
Mcu.Pin58=PG4
Mcu.Pin59=PC8
Mcu.Pin6=PC14-OSC32_IN
Mcu.Pin60=PC9
Mcu.Pin61=PA8
Mcu.Pin62=PA9
Mcu.Pin63=PA10
Mcu.Pin64=PA11
Mcu.Pin65=PA12
Mcu.Pin66=PA13
Mcu.Pin67=PF6
Mcu.Pin68=PA14
Mcu.Pin69=PA15
Mcu.Pin7=PC15-OSC32_OUT
Mcu.Pin70=PC10
Mcu.Pin71=PC11
Mcu.Pin72=PC12
Mcu.Pin73=PG5
Mcu.Pin74=PG6
Mcu.Pin75=PG7
Mcu.Pin76=PG8
Mcu.Pin77=PG9
Mcu.Pin78=PD0
Mcu.Pin79=PD1
Mcu.Pin8=PF3
Mcu.Pin80=PD2
Mcu.Pin81=PD4
Mcu.Pin82=PD5
Mcu.Pin83=PD6
Mcu.Pin84=PD7
Mcu.Pin85=PB3
Mcu.Pin86=PB4
Mcu.Pin87=PB5
Mcu.Pin88=PB8-BOOT0
Mcu.Pin89=PB9
Mcu.Pin9=PF4
Mcu.Pin90=PE0
Mcu.Pin91=PE1
Mcu.Pin92=VP_COMP6_VS_VREFINT
Mcu.Pin93=VP_HRTIM1_VS_hrtimMasterTimerNoOutput
Mcu.Pin94=VP_SYS_VS_Systick
Mcu.Pin95=VP_TIM8_VS_ControllerModeTrigger
Mcu.Pin96=VP_TIM8_VS_ClockSourceINT
Mcu.Pin97=VP_TIM8_VS_ClockSourceITR
Mcu.PinsNb=98
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G474QETx
MxCube.Version=5.2.0
MxDb.Version=DB.5.0.20
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:true\:false\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:14\:0\:true\:false\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:true\:false\:false\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
PA0.GPIOParameters=GPIO_Label
PA0.GPIO_Label=MFX_IRQ_OUT
PA0.Locked=true
PA0.Signal=GPXTI0
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=ADC_OUTN
PA1.Locked=true
PA1.Mode=IN2-Single-Ended
PA1.Signal=ADC1_IN2
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=USART1_RX [STLink_VCP_TX]
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=USB_DM
PA11.Locked=true
PA11.Mode=Device
PA11.Signal=USB_DM
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=USB_DP
PA12.Locked=true
PA12.Mode=Device
PA12.Signal=USB_DP
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=JTMS-SWDIO
PA13.Locked=true
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.GPIOParameters=GPIO_Label
PA14.GPIO_Label=JTCK-SWCLK
PA14.Locked=true
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=JTDI
PA15.Locked=true
PA15.Signal=SYS_JTDI
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=DAC
PA4.Locked=true
PA4.Signal=COMP_DAC11_group
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=MC_DAC_OUT2
PA5.Locked=true
PA5.Signal=COMP_DAC12_group
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=SAI_SCK_A
PA8.Locked=true
PA8.Mode=SAI_A_MasterWithClock
PA8.Signal=SAI1_SCK_A
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=USART1_TX [STLink_VCP_RX]
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.GPIOParameters=GPIO_Label
PB10.GPIO_Label=OPAMP4_VINM
PB10.Locked=true
PB10.Signal=OPAMP4_VINM
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=OPAMP4/COMP6_INP
PB11.Locked=true
PB11.Mode=INP
PB11.Signal=COMP6_INP
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=OPAMP4_VOUT
PB12.Locked=true
PB12.Signal=OPAMP4_VOUT
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=FDCAN2_TX/PD_CC1
PB13.Locked=true
PB13.Mode=FDCANSlave
PB13.Signal=FDCAN2_TX
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=SPI2_MISO
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=SPI2_MOSI
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB2.GPIOParameters=GPIO_Label
PB2.GPIO_Label=DISCHARGE
PB2.Locked=true
PB2.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=JTDO-SWO
PB3.Locked=true
PB3.Signal=SYS_JTDO-SWO
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=JTRST/PD_CC2
PB4.Locked=true
PB4.Signal=SYS_JTRST
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=FDCAN2_RX
PB5.Locked=true
PB5.Mode=FDCANSlave
PB5.Signal=FDCAN2_RX
PB8-BOOT0.GPIOParameters=GPIO_Label
PB8-BOOT0.GPIO_Label=BOOT0/FDCAN1_RX
PB8-BOOT0.Locked=true
PB8-BOOT0.Mode=FDCANMaster
PB8-BOOT0.Signal=FDCAN1_RX
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=FDCAN1_TX
PB9.Locked=true
PB9.Mode=FDCANMaster
PB9.Signal=FDCAN1_TX
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=VSENSE
PC0.Locked=true
PC0.Mode=IN6-Single-Ended
PC0.Signal=ADC2_IN6
PC10.GPIOParameters=GPIO_Label
PC10.GPIO_Label=SmartCard_IO
PC10.Locked=true
PC10.Mode=SmartCard_With_Clock
PC10.Signal=USART3_TX
PC11.GPIOParameters=GPIO_Label
PC11.GPIO_Label=SOURCE_EN
PC11.Locked=true
PC11.Signal=GPIO_Output
PC12.GPIOParameters=GPIO_Label
PC12.GPIO_Label=SmartCard_CLK
PC12.Locked=true
PC12.Mode=SmartCard_With_Clock
PC12.Signal=USART3_CK
PC13.GPIOParameters=GPIO_Label
PC13.GPIO_Label=TAMPER_KEY
PC13.Locked=true
PC13.Signal=GPIO_Input
PC14-OSC32_IN.GPIOParameters=GPIO_Label
PC14-OSC32_IN.GPIO_Label=PC14-OSC32_IN
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.GPIOParameters=GPIO_Label
PC15-OSC32_OUT.GPIO_Label=PC15-OSC32_OUT
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=CMOP6_OUT
PC6.Locked=true
PC6.Mode=ExternalOutput
PC6.Signal=COMP6_OUT
PC7.GPIOParameters=GPIO_Label
PC7.GPIO_Label=MFX_WAKEUP
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.GPIOParameters=GPIO_Label
PC8.GPIO_Label=BK_Drive
PC8.Locked=true
PC8.Mode=Output_TE1
PC8.Signal=HRTIM1_CHE1
PC9.GPIOParameters=GPIO_Label
PC9.GPIO_Label=LCD_CS
PC9.Locked=true
PC9.Signal=GPIO_Output
PCC.Checker=true
PCC.Line=STM32G4x4
PCC.MCU=STM32G474Q(B-C-E)Tx
PCC.PartNumber=STM32G474QETx
PCC.Seq0=0
PCC.Series=STM32G4
PCC.Temperature=25
PCC.Vdd=3.0
PD0.GPIOParameters=GPIO_Label
PD0.GPIO_Label=FMC_D2
PD0.Locked=true
PD0.Signal=FMC_D2_DA2
PD1.GPIOParameters=GPIO_Label
PD1.GPIO_Label=FMC_D3
PD1.Locked=true
PD1.Signal=FMC_D3_DA3
PD10.GPIOParameters=GPIO_Label
PD10.GPIO_Label=FMC_D15
PD10.Locked=true
PD10.Signal=FMC_D15_DA15
PD11.GPIOParameters=GPIO_Label
PD11.GPIO_Label=FMC_A16
PD11.Locked=true
PD11.Signal=FMC_A16_CLE
PD12.GPIOParameters=GPIO_Label
PD12.GPIO_Label=FMC_A17
PD12.Locked=true
PD12.Signal=FMC_A17_ALE
PD13.GPIOParameters=GPIO_Label
PD13.GPIO_Label=FMC_A18
PD13.Locked=true
PD13.Signal=FMC_A18
PD14.GPIOParameters=GPIO_Label
PD14.GPIO_Label=FMC_D0
PD14.Locked=true
PD14.Signal=FMC_D0_DA0
PD15.GPIOParameters=GPIO_Label
PD15.GPIO_Label=FMC_D1
PD15.Locked=true
PD15.Signal=FMC_D1_DA1
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=RS485_DIR
PD2.Locked=true
PD2.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Label
PD4.GPIO_Label=FMC_NOE
PD4.Locked=true
PD4.Signal=FMC_NOE
PD5.GPIOParameters=GPIO_Label
PD5.GPIO_Label=FMC_NWE
PD5.Locked=true
PD5.Signal=FMC_NWE
PD6.GPIOParameters=GPIO_Label
PD6.GPIO_Label=SAI_SD_A
PD6.Locked=true
PD6.Mode=SAI_A_MasterWithClock
PD6.Signal=SAI1_SD_A
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=FMC_NE1
PD7.Locked=true
PD7.Mode=NorPsramChipSelect1_1
PD7.Signal=FMC_NE1
PD8.GPIOParameters=GPIO_Label
PD8.GPIO_Label=FMC_D13
PD8.Locked=true
PD8.Signal=FMC_D13_DA13
PD9.GPIOParameters=GPIO_Label
PD9.GPIO_Label=FMC_D14
PD9.Locked=true
PD9.Signal=FMC_D14_DA14
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=FMC_NBL0
PE0.Locked=true
PE0.Signal=FMC_NBL0
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=FMC_NBL1
PE1.Locked=true
PE1.Signal=FMC_NBL1
PE10.GPIOParameters=GPIO_Label
PE10.GPIO_Label=FMC_D7
PE10.Locked=true
PE10.Signal=FMC_D7_DA7
PE11.GPIOParameters=GPIO_Label
PE11.GPIO_Label=FMC_D8
PE11.Locked=true
PE11.Signal=FMC_D8_DA8
PE12.GPIOParameters=GPIO_Label
PE12.GPIO_Label=FMC_D9
PE12.Locked=true
PE12.Signal=FMC_D9_DA9
PE13.GPIOParameters=GPIO_Label
PE13.GPIO_Label=FMC_D10
PE13.Locked=true
PE13.Signal=FMC_D10_DA10
PE14.GPIOParameters=GPIO_Label
PE14.GPIO_Label=FMC_D11
PE14.Locked=true
PE14.Signal=FMC_D11_DA11
PE15.GPIOParameters=GPIO_Label
PE15.GPIO_Label=FMC_D12
PE15.Locked=true
PE15.Signal=FMC_D12_DA12
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=SAI_MCLK_A
PE2.Locked=true
PE2.Mode=SAI_A_MasterWithClock
PE2.Signal=SAI1_MCLK_A
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=SAI_SD_B
PE3.Locked=true
PE3.Mode=SAI_B_SyncSlave
PE3.Signal=SAI1_SD_B
PE4.GPIOParameters=GPIO_Label
PE4.GPIO_Label=SAI_FS_A
PE4.Locked=true
PE4.Mode=SAI_A_MasterWithClock
PE4.Signal=SAI1_FS_A
PE5.GPIOParameters=GPIO_Label
PE5.GPIO_Label=TRACED2
PE5.Locked=true
PE5.Signal=SYS_TRACED2
PE6.GPIOParameters=GPIO_Label
PE6.GPIO_Label=TRACED3
PE6.Locked=true
PE6.Signal=SYS_TRACED3
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=FMC_D4
PE7.Locked=true
PE7.Signal=FMC_D4_DA4
PE8.GPIOParameters=GPIO_Label
PE8.GPIO_Label=FMC_D5
PE8.Locked=true
PE8.Signal=FMC_D5_DA5
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=FMC_D6
PE9.Locked=true
PE9.Signal=FMC_D6_DA6
PF0-OSC_IN.GPIOParameters=GPIO_Label
PF0-OSC_IN.GPIO_Label=PF0-OSC_IN
PF0-OSC_IN.Locked=true
PF0-OSC_IN.Mode=HSE-External-Oscillator
PF0-OSC_IN.Signal=RCC_OSC_IN
PF1-OSC_OUT.GPIOParameters=GPIO_Label
PF1-OSC_OUT.GPIO_Label=PF1-OSC_OUT
PF1-OSC_OUT.Locked=true
PF1-OSC_OUT.Mode=HSE-External-Oscillator
PF1-OSC_OUT.Signal=RCC_OSC_OUT
PF10.GPIOParameters=GPIO_Label
PF10.GPIO_Label=FMC_A0
PF10.Locked=true
PF10.Signal=FMC_A0
PF11.GPIOParameters=GPIO_Label
PF11.GPIO_Label=LED3 [red]
PF11.Locked=true
PF11.Signal=GPIO_Output
PF12.GPIOParameters=GPIO_Label
PF12.GPIO_Label=FMC_A6
PF12.Locked=true
PF12.Signal=FMC_A6
PF13.GPIOParameters=GPIO_Label
PF13.GPIO_Label=FMC_A7
PF13.Locked=true
PF13.Signal=FMC_A7
PF14.GPIOParameters=GPIO_Label
PF14.GPIO_Label=FMC_A8
PF14.Locked=true
PF14.Signal=FMC_A8
PF15.GPIOParameters=GPIO_Label
PF15.GPIO_Label=FMC_A9
PF15.Locked=true
PF15.Signal=FMC_A9
PF2.GPIOParameters=GPIO_Label
PF2.GPIO_Label=FMC_A2
PF2.Locked=true
PF2.Signal=FMC_A2
PF3.GPIOParameters=GPIO_Label
PF3.GPIO_Label=FMC_A3
PF3.Locked=true
PF3.Signal=FMC_A3
PF4.GPIOParameters=GPIO_Label
PF4.GPIO_Label=FMC_A4
PF4.Locked=true
PF4.Signal=FMC_A4
PF5.GPIOParameters=GPIO_Label
PF5.GPIO_Label=FMC_A5
PF5.Locked=true
PF5.Signal=FMC_A5
PF6.GPIOParameters=GPIO_Label
PF6.GPIO_Label=Audio_INT
PF6.Locked=true
PF6.Signal=GPXTI6
PF7.GPIOParameters=GPIO_Label
PF7.GPIO_Label=FMC_A1
PF7.Locked=true
PF7.Signal=FMC_A1
PF8.GPIOParameters=GPIO_Label
PF8.GPIO_Label=uSD_CS
PF8.Locked=true
PF8.Signal=GPIO_Output
PF9.GPIOParameters=GPIO_Label
PF9.GPIO_Label=SPI2_SCK
PF9.Locked=true
PF9.Mode=Full_Duplex_Master
PF9.Signal=SPI2_SCK
PG0.GPIOParameters=GPIO_Label
PG0.GPIO_Label=FMC_A10
PG0.Locked=true
PG0.Signal=FMC_A10
PG1.GPIOParameters=GPIO_Label
PG1.GPIO_Label=FMC_A11
PG1.Locked=true
PG1.Signal=FMC_A11
PG2.GPIOParameters=GPIO_Label
PG2.GPIO_Label=FMC_A12
PG2.Locked=true
PG2.Signal=FMC_A12
PG3.GPIOParameters=GPIO_Label
PG3.GPIO_Label=FMC_A13
PG3.Locked=true
PG3.Signal=FMC_A13
PG4.GPIOParameters=GPIO_Label
PG4.GPIO_Label=FMC_A14
PG4.Locked=true
PG4.Signal=FMC_A14
PG5.GPIOParameters=GPIO_Label
PG5.GPIO_Label=FMC_A15
PG5.Locked=true
PG5.Signal=FMC_A15
PG6.GPIOParameters=GPIO_Label
PG6.GPIO_Label=I2C3_SMBA
PG6.Locked=true
PG6.Mode=SMBus-Alert-mode
PG6.Signal=I2C3_SMBA
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=I2C3_SCL
PG7.Locked=true
PG7.Mode=SMBus-Alert-mode
PG7.Signal=I2C3_SCL
PG8.GPIOParameters=GPIO_Label
PG8.GPIO_Label=I2C3_SDA
PG8.Locked=true
PG8.Mode=SMBus-Alert-mode
PG8.Signal=I2C3_SDA
PG9.GPIOParameters=GPIO_Label
PG9.GPIO_Label=LED1 [green]
PG9.Locked=true
PG9.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G474QETx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.0.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=STCubeGenerated.ioc
ProjectManager.ProjectName=STCubeGenerated
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_ADC1_Init-ADC1-false-HAL-true,4-MX_ADC2_Init-ADC2-false-HAL-true,5-MX_COMP6_Init-COMP6-false-HAL-true,6-MX_DAC1_Init-DAC1-false-HAL-true,7-MX_FDCAN1_Init-FDCAN1-false-HAL-true,8-MX_FDCAN2_Init-FDCAN2-false-HAL-true,9-MX_FMC_Init-FMC-false-HAL-true,10-MX_HRTIM1_Init-HRTIM1-false-HAL-true,11-MX_I2C3_SMBUS_Init-I2C3-false-HAL-true,12-MX_SAI1_Init-SAI1-false-HAL-true,13-MX_SPI2_Init-SPI2-false-HAL-true,14-MX_TIM8_Init-TIM8-false-HAL-true,15-MX_USART1_UART_Init-USART1-false-HAL-true,16-MX_USART3_SMARTCARD_Init-USART3-false-HAL-true,17-MX_USB_PCD_Init-USB-false-HAL-true
RCC.ADC12Freq_Value=170000000
RCC.ADC345Freq_Value=170000000
RCC.AHBFreq_Value=170000000
RCC.APB1Freq_Value=170000000
RCC.APB1TimFreq_Value=170000000
RCC.APB2Freq_Value=170000000
RCC.APB2TimFreq_Value=170000000
RCC.CK48CLockSelection=RCC_USBCLKSOURCE_HSI48
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=170000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=170000000
RCC.FDCANFreq_Value=170000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=170000000
RCC.HSE_VALUE=24000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=170000000
RCC.I2C2Freq_Value=170000000
RCC.I2C3Freq_Value=170000000
RCC.I2C4Freq_Value=170000000
RCC.I2SFreq_Value=170000000
RCC.IPParameters=ADC12Freq_Value,ADC345Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CK48CLockSelection,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSI_VALUE,MCO1PinFreq_Value,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PWRFreq_Value,QSPIFreq_Value,RNGFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=170000000
RCC.LPUART1Freq_Value=170000000
RCC.LSCOPinFreq_Value=32000
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLM=RCC_PLLM_DIV4
RCC.PLLN=85
RCC.PLLPoutputFreq_Value=170000000
RCC.PLLQoutputFreq_Value=170000000
RCC.PLLRCLKFreq_Value=170000000
RCC.PWRFreq_Value=170000000
RCC.QSPIFreq_Value=170000000
RCC.RNGFreq_Value=48000000
RCC.SAI1Freq_Value=170000000
RCC.SYSCLKFreq_VALUE=170000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=170000000
RCC.UART5Freq_Value=170000000
RCC.USART1Freq_Value=170000000
RCC.USART2Freq_Value=170000000
RCC.USART3Freq_Value=170000000
RCC.USBFreq_Value=48000000
RCC.VCOInputFreq_Value=4000000
RCC.VCOOutputFreq_Value=340000000
SAI1.ErrorAudioFreq-SAI_A_MasterWithClock=72.93 %
SAI1.IPParameters=Instance-SAI_A_MasterWithClock,VirtualMode-SAI_A_MasterWithClock,Synchro-SAI_A_MasterWithClock,MClockEnable-SAI_A_MasterWithClock,Instance-SAI_B_SyncSlave,VirtualMode-SAI_B_SyncSlave,Synchro-SAI_B_SyncSlave,RealAudioFreq-SAI_A_MasterWithClock,ErrorAudioFreq-SAI_A_MasterWithClock,MckOutput-SAI_A_MasterWithClock
SAI1.Instance-SAI_A_MasterWithClock=SAI$Index_Block_A
SAI1.Instance-SAI_B_SyncSlave=SAI$Index_Block_B
SAI1.MClockEnable-SAI_A_MasterWithClock=SAI_MASTERCLOCK_ENABLE
SAI1.MckOutput-SAI_A_MasterWithClock=SAI_MCK_OUTPUT_ENABLE
SAI1.RealAudioFreq-SAI_A_MasterWithClock=332.031 KHz
SAI1.Synchro-SAI_A_MasterWithClock=SAI_ASYNCHRONOUS
SAI1.Synchro-SAI_B_SyncSlave=SAI_SYNCHRONOUS
SAI1.VirtualMode-SAI_A_MasterWithClock=VM_MASTER
SAI1.VirtualMode-SAI_B_SyncSlave=VM_SLAVE
SH.COMP_DAC11_group.0=DAC1_OUT1,DAC_OUT1_ExtAndInt
SH.COMP_DAC11_group.ConfNb=1
SH.COMP_DAC12_group.0=DAC1_OUT2,DAC_OUT2_ExtAndInt
SH.COMP_DAC12_group.ConfNb=1
SH.FMC_A0.0=FMC_A0,19b-a1
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1,19b-a1
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10,19b-a1
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11,19b-a1
SH.FMC_A11.ConfNb=1
SH.FMC_A12.0=FMC_A12,19b-a1
SH.FMC_A12.ConfNb=1
SH.FMC_A13.0=FMC_A13,19b-a1
SH.FMC_A13.ConfNb=1
SH.FMC_A14.0=FMC_A14,19b-a1
SH.FMC_A14.ConfNb=1
SH.FMC_A15.0=FMC_A15,19b-a1
SH.FMC_A15.ConfNb=1
SH.FMC_A16_CLE.0=FMC_A16,19b-a1
SH.FMC_A16_CLE.ConfNb=1
SH.FMC_A17_ALE.0=FMC_A17,19b-a1
SH.FMC_A17_ALE.ConfNb=1
SH.FMC_A18.0=FMC_A18,19b-a1
SH.FMC_A18.ConfNb=1
SH.FMC_A2.0=FMC_A2,19b-a1
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3,19b-a1
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4,19b-a1
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5,19b-a1
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6,19b-a1
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7,19b-a1
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8,19b-a1
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9,19b-a1
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0,16b-d1
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10,16b-d1
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11,16b-d1
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12,16b-d1
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13,16b-d1
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14,16b-d1
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15,16b-d1
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1,16b-d1
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2,16b-d1
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3,16b-d1
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4,16b-d1
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5,16b-d1
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6,16b-d1
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7,16b-d1
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8,16b-d1
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9,16b-d1
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,2ByteEnable1
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,2ByteEnable1
SH.FMC_NBL1.ConfNb=1
SH.FMC_NOE.0=FMC_NOE,Sram1
SH.FMC_NOE.ConfNb=1
SH.FMC_NWE.0=FMC_NWE,Sram1
SH.FMC_NWE.ConfNb=1
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
SH.GPXTI6.0=GPIO_EXTI6
SH.GPXTI6.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_16
SPI2.CalculateBaudRate=10.625 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
USART1.IPParameters=VirtualMode-Asynchronous,WordLength
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART1.WordLength=WORDLENGTH_8B
USART3.IPParameters=VirtualMode-SmartCard_With_Clock,VirtualClockMode,Prescaler
USART3.Prescaler=20
USART3.VirtualClockMode=VM_SMARTCARD
USART3.VirtualMode-SmartCard_With_Clock=VM_SMARTCARD
VP_COMP6_VS_VREFINT.Mode=VREFINT
VP_COMP6_VS_VREFINT.Signal=COMP6_VS_VREFINT
VP_HRTIM1_VS_hrtimMasterTimerNoOutput.Mode=MasterTimer
VP_HRTIM1_VS_hrtimMasterTimerNoOutput.Signal=HRTIM1_VS_hrtimMasterTimerNoOutput
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM8_VS_ClockSourceINT.Mode=Internal
VP_TIM8_VS_ClockSourceINT.Signal=TIM8_VS_ClockSourceINT
VP_TIM8_VS_ClockSourceITR.Mode=TriggerSource_ITR0
VP_TIM8_VS_ClockSourceITR.Signal=TIM8_VS_ClockSourceITR
VP_TIM8_VS_ControllerModeTrigger.Mode=Trigger Mode
VP_TIM8_VS_ControllerModeTrigger.Signal=TIM8_VS_ControllerModeTrigger
board=STM32G474E-EVAL
boardIOC=true
