#MicroXplorer Configuration settings - do not modify
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_11
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC1.CommonPathInternal=null|null|null|null
ADC1.ContinuousConvMode=ENABLE
ADC1.DMAContinuousRequests=ENABLE
ADC1.IPParameters=Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversionFlag,master,DMAContinuousRequests,ContinuousConvMode,ClockPrescaler,CommonPathInternal
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Rank-2\#ChannelRegularConversion=1
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.master=1
ADC2.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_15
ADC2.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC2.CommonPathInternal=null|null|null|null
ADC2.ContinuousConvMode=ENABLE
ADC2.DMAContinuousRequests=ENABLE
ADC2.IPParameters=Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescaler,DMAContinuousRequests,ContinuousConvMode,CommonPathInternal
ADC2.NbrOfConversionFlag=1
ADC2.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.Rank-2\#ChannelRegularConversion=1
ADC2.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.EventEnable=DISABLE
Dma.ADC1.1.Instance=DMA1_Channel2
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_CIRCULAR
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.1.Priority=DMA_PRIORITY_MEDIUM
Dma.ADC1.1.RequestNumber=1
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.1.SignalID=NONE
Dma.ADC1.1.SyncEnable=DISABLE
Dma.ADC1.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.1.SyncRequestNumber=1
Dma.ADC1.1.SyncSignalID=NONE
Dma.ADC2.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC2.2.EventEnable=DISABLE
Dma.ADC2.2.Instance=DMA1_Channel3
Dma.ADC2.2.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC2.2.MemInc=DMA_MINC_ENABLE
Dma.ADC2.2.Mode=DMA_CIRCULAR
Dma.ADC2.2.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC2.2.PeriphInc=DMA_PINC_DISABLE
Dma.ADC2.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC2.2.Priority=DMA_PRIORITY_MEDIUM
Dma.ADC2.2.RequestNumber=1
Dma.ADC2.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC2.2.SignalID=NONE
Dma.ADC2.2.SyncEnable=DISABLE
Dma.ADC2.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC2.2.SyncRequestNumber=1
Dma.ADC2.2.SyncSignalID=NONE
Dma.Request0=USART1_RX
Dma.Request1=ADC1
Dma.Request2=ADC2
Dma.Request3=TIM3_CH1
Dma.RequestsNb=4
Dma.TIM3_CH1.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.TIM3_CH1.3.EventEnable=DISABLE
Dma.TIM3_CH1.3.Instance=DMA1_Channel4
Dma.TIM3_CH1.3.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.TIM3_CH1.3.MemInc=DMA_MINC_ENABLE
Dma.TIM3_CH1.3.Mode=DMA_CIRCULAR
Dma.TIM3_CH1.3.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.TIM3_CH1.3.PeriphInc=DMA_PINC_DISABLE
Dma.TIM3_CH1.3.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.TIM3_CH1.3.Priority=DMA_PRIORITY_LOW
Dma.TIM3_CH1.3.RequestNumber=1
Dma.TIM3_CH1.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.TIM3_CH1.3.SignalID=NONE
Dma.TIM3_CH1.3.SyncEnable=DISABLE
Dma.TIM3_CH1.3.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.TIM3_CH1.3.SyncRequestNumber=1
Dma.TIM3_CH1.3.SyncSignalID=NONE
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.EventEnable=DISABLE
Dma.USART1_RX.0.Instance=DMA1_Channel1
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART1_RX.0.Priority=DMA_PRIORITY_HIGH
Dma.USART1_RX.0.RequestNumber=1
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART1_RX.0.SignalID=NONE
Dma.USART1_RX.0.SyncEnable=DISABLE
Dma.USART1_RX.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART1_RX.0.SyncRequestNumber=1
Dma.USART1_RX.0.SyncSignalID=NONE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32G431RBT6TR
Mcu.Family=STM32G4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=USART1
Mcu.IP2=DMA
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=RTC
Mcu.IP6=SYS
Mcu.IP7=TIM1
Mcu.IP8=TIM2
Mcu.IP9=TIM3
Mcu.IPNb=11
Mcu.Name=STM32G431R(6-8-B)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PB0
Mcu.Pin11=PB1
Mcu.Pin12=PB2
Mcu.Pin13=PB12
Mcu.Pin14=PB15
Mcu.Pin15=PC6
Mcu.Pin16=PC7
Mcu.Pin17=PC8
Mcu.Pin18=PC9
Mcu.Pin19=PA8
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PA9
Mcu.Pin21=PA10
Mcu.Pin22=PC10
Mcu.Pin23=PC11
Mcu.Pin24=PC12
Mcu.Pin25=PD2
Mcu.Pin26=PB4
Mcu.Pin27=PB5
Mcu.Pin28=PB6
Mcu.Pin29=PB7
Mcu.Pin3=PF0-OSC_IN
Mcu.Pin30=PB8-BOOT0
Mcu.Pin31=PB9
Mcu.Pin32=VP_RTC_VS_RTC_Activate
Mcu.Pin33=VP_RTC_VS_RTC_Calendar
Mcu.Pin34=VP_SYS_VS_Systick
Mcu.Pin35=VP_SYS_VS_DBSignals
Mcu.Pin36=VP_TIM1_VS_ClockSourceINT
Mcu.Pin37=VP_TIM2_VS_ClockSourceINT
Mcu.Pin38=VP_TIM3_VS_ControllerModeReset
Mcu.Pin39=VP_TIM3_VS_ClockSourceINT
Mcu.Pin4=PF1-OSC_OUT
Mcu.Pin5=PC0
Mcu.Pin6=PA0
Mcu.Pin7=PA1
Mcu.Pin8=PC4
Mcu.Pin9=PC5
Mcu.PinsNb=40
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G431RBTx
MxCube.Version=6.12.0
MxDb.Version=DB.6.0.120
NVIC.BusFault_IRQn=true\:0\:0\:true\:false\:true\:false\:false\:false
NVIC.DMA1_Channel1_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Channel2_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Channel3_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:true
NVIC.DMA1_Channel4_IRQn=true\:1\:0\:true\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:true\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0.GPIOParameters=GPIO_PuPd
PA0.GPIO_PuPd=GPIO_PULLUP
PA0.Locked=true
PA0.Signal=GPIO_Input
PA1.Signal=S_TIM2_CH2
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_PuPd
PB0.GPIO_PuPd=GPIO_PULLUP
PB0.Locked=true
PB0.Signal=GPIO_Input
PB1.GPIOParameters=GPIO_PuPd
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.Locked=true
PB1.Signal=GPIO_Input
PB12.Mode=IN11-Single-Ended
PB12.Signal=ADC1_IN11
PB15.Mode=IN15-Single-Ended
PB15.Signal=ADC2_IN15
PB2.GPIOParameters=GPIO_PuPd
PB2.GPIO_PuPd=GPIO_PULLUP
PB2.Locked=true
PB2.Signal=GPIO_Input
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB6.GPIO_PuPd=GPIO_PULLUP
PB6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB6.Locked=true
PB6.Signal=GPIO_Output
PB7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB7.GPIO_PuPd=GPIO_PULLUP
PB7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB7.Locked=true
PB7.Signal=GPIO_Output
PB8-BOOT0.Locked=true
PB8-BOOT0.Signal=GPIO_Output
PB9.Locked=true
PB9.Signal=GPIO_Output
PC0.Signal=S_TIM1_CH1
PC10.GPIOParameters=PinState
PC10.Locked=true
PC10.PinState=GPIO_PIN_SET
PC10.Signal=GPIO_Output
PC11.GPIOParameters=PinState
PC11.Locked=true
PC11.PinState=GPIO_PIN_SET
PC11.Signal=GPIO_Output
PC12.GPIOParameters=PinState
PC12.Locked=true
PC12.PinState=GPIO_PIN_SET
PC12.Signal=GPIO_Output
PC13.GPIOParameters=PinState
PC13.Locked=true
PC13.PinState=GPIO_PIN_SET
PC13.Signal=GPIO_Output
PC14-OSC32_IN.GPIOParameters=PinState
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.PinState=GPIO_PIN_SET
PC14-OSC32_IN.Signal=GPIO_Output
PC15-OSC32_OUT.GPIOParameters=PinState
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.PinState=GPIO_PIN_SET
PC15-OSC32_OUT.Signal=GPIO_Output
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.GPIOParameters=PinState
PC8.Locked=true
PC8.PinState=GPIO_PIN_SET
PC8.Signal=GPIO_Output
PC9.GPIOParameters=PinState
PC9.Locked=true
PC9.PinState=GPIO_PIN_SET
PC9.Signal=GPIO_Output
PD2.GPIOParameters=PinState
PD2.Locked=true
PD2.PinState=GPIO_PIN_RESET
PD2.Signal=GPIO_Output
PF0-OSC_IN.Mode=HSE-External-Oscillator
PF0-OSC_IN.Signal=RCC_OSC_IN
PF1-OSC_OUT.Mode=HSE-External-Oscillator
PF1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=..\\..\\\u7C73\u918B\u7535\u5B50\u5DE5\u4F5C\u5BA4\\STM32\\STM32Cube_FW_G4_V1.4.0
ProjectManager.DefaultFWLocation=false
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G431RBTx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.6.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=STM32_Xifeng.ioc
ProjectManager.ProjectName=STM32_Xifeng
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_ADC2_Init-ADC2-false-HAL-true,7-MX_RTC_Init-RTC-false-HAL-true,8-MX_TIM1_Init-TIM1-false-HAL-true,9-MX_TIM3_Init-TIM3-false-HAL-true,10-MX_TIM2_Init-TIM2-false-HAL-true
RCC.ADC12Freq_Value=80000000
RCC.AHBFreq_Value=80000000
RCC.APB1Freq_Value=80000000
RCC.APB1TimFreq_Value=80000000
RCC.APB2Freq_Value=80000000
RCC.APB2TimFreq_Value=80000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=80000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=80000000
RCC.FDCANFreq_Value=80000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=80000000
RCC.HSE_VALUE=24000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=80000000
RCC.I2C2Freq_Value=80000000
RCC.I2C3Freq_Value=80000000
RCC.I2SFreq_Value=80000000
RCC.IPParameters=ADC12Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PWRFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=80000000
RCC.LPUART1Freq_Value=80000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLM=RCC_PLLM_DIV2
RCC.PLLN=20
RCC.PLLPoutputFreq_Value=80000000
RCC.PLLQoutputFreq_Value=80000000
RCC.PLLRCLKFreq_Value=80000000
RCC.PWRFreq_Value=80000000
RCC.RNGFreq_Value=80000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=80000000
RCC.SYSCLKFreq_VALUE=80000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=80000000
RCC.USART1Freq_Value=80000000
RCC.USART2Freq_Value=80000000
RCC.USART3Freq_Value=80000000
RCC.USBFreq_Value=80000000
RCC.VCOInputFreq_Value=8000000
RCC.VCOOutputFreq_Value=160000000
RTC.AsynchPrediv=127
RTC.Format=RTC_FORMAT_BIN
RTC.Hours=23
RTC.IPParameters=AsynchPrediv,SynchPrediv,Format,Hours,Minutes,Seconds
RTC.Minutes=59
RTC.Seconds=55
RTC.SynchPrediv=255
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,PWM Generation2 CH2
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,TriggerSource_TI1FP1
SH.S_TIM3_CH1.1=TIM3_CH1,Input_Capture1_from_TI1
SH.S_TIM3_CH1.ConfNb=2
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.IPParameters=Channel-PWM Generation1 CH1,Prescaler,PeriodNoDither,PulseNoDither_1
TIM1.PeriodNoDither=1000-1
TIM1.Prescaler=170-1
TIM1.PulseNoDither_1=50
TIM2.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM2.IPParameters=Channel-PWM Generation2 CH2,PeriodNoDither,Prescaler,PulseNoDither_2
TIM2.PeriodNoDither=1000-1
TIM2.Prescaler=170-1
TIM2.PulseNoDither_2=50
TIM3.Channel-Input_Capture1_from_TI1=TIM_CHANNEL_1
TIM3.IPParameters=Channel-Input_Capture1_from_TI1,Prescaler,PeriodNoDither
TIM3.PeriodNoDither=65535
TIM3.Prescaler=80-1
USART1.BaudRate=9600
USART1.IPParameters=VirtualMode-Asynchronous,BaudRate
USART1.VirtualMode-Asynchronous=VM_ASYNC
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_SYS_VS_DBSignals.Signal=SYS_VS_DBSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM3_VS_ControllerModeReset.Mode=Reset Mode
VP_TIM3_VS_ControllerModeReset.Signal=TIM3_VS_ControllerModeReset
board=custom
