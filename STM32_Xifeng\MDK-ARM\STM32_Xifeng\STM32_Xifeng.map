Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    startup_stm32g431xx.o(RESET) refers to startup_stm32g431xx.o(.text) for Reset_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler) for DMA1_Channel3_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler) for DMA1_Channel4_IRQHandler
    startup_stm32g431xx.o(RESET) refers to stm32g4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32g431xx.o(.text) refers to system_stm32g4xx.o(.text.SystemInit) for SystemInit
    startup_stm32g431xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32g4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.main) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(.text.main) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC1_Init) for MX_ADC1_Init
    main.o(.text.main) refers to adc.o(.text.MX_ADC2_Init) for MX_ADC2_Init
    main.o(.text.main) refers to rtc.o(.text.MX_RTC_Init) for MX_RTC_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM2_Init) for MX_TIM2_Init
    main.o(.text.main) refers to system.o(.text.system_init) for system_init
    main.o(.text.main) refers to key_app.o(.text.key_init) for key_init
    main.o(.text.main) refers to uart_app.o(.bss.usart_rb) for usart_rb
    main.o(.text.main) refers to ringbuffer.o(.text.ringbuffer_init) for ringbuffer_init
    main.o(.text.main) refers to adc_app.o(.bss.dma_buff) for dma_buff
    main.o(.text.main) refers to adc.o(.bss.hadc1) for hadc1
    main.o(.text.main) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(.text.main) refers to adc.o(.bss.hadc2) for hadc2
    main.o(.text.main) refers to tim.o(.bss.htim3) for htim3
    main.o(.text.main) refers to tim_app.o(.bss.tim_ic_buffer) for tim_ic_buffer
    main.o(.text.main) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for HAL_TIM_IC_Start_DMA
    main.o(.text.main) refers to tim.o(.bss.htim1) for htim1
    main.o(.text.main) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    main.o(.text.main) refers to tim.o(.bss.htim2) for htim2
    main.o(.text.main) refers to lcd.o(.text.LCD_Init) for LCD_Init
    main.o(.text.main) refers to lcd.o(.text.LCD_Clear) for LCD_Clear
    main.o(.text.main) refers to lcd.o(.text.LCD_SetTextColor) for LCD_SetTextColor
    main.o(.text.main) refers to lcd.o(.text.LCD_SetBackColor) for LCD_SetBackColor
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(.text.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC1_Init) refers to adc.o(.bss.hadc1) for hadc1
    adc.o(.text.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC1_Init) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel) for HAL_ADCEx_MultiModeConfigChannel
    adc.o(.text.MX_ADC1_Init) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.ARM.exidx.text.MX_ADC1_Init) refers to adc.o(.text.MX_ADC1_Init) for [Anonymous Symbol]
    adc.o(.text.MX_ADC2_Init) refers to adc.o(.bss.hadc2) for hadc2
    adc.o(.text.MX_ADC2_Init) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(.text.MX_ADC2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.MX_ADC2_Init) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(.ARM.exidx.text.MX_ADC2_Init) refers to adc.o(.text.MX_ADC2_Init) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(.text.HAL_ADC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    adc.o(.text.HAL_ADC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.HAL_RCC_ADC12_CLK_ENABLED) for HAL_RCC_ADC12_CLK_ENABLED
    adc.o(.text.HAL_ADC_MspInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    adc.o(.text.HAL_ADC_MspInit) refers to adc.o(.bss.hdma_adc2) for hdma_adc2
    adc.o(.text.HAL_ADC_MspInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    adc.o(.text.HAL_ADC_MspDeInit) refers to adc.o(.bss.HAL_RCC_ADC12_CLK_ENABLED) for HAL_RCC_ADC12_CLK_ENABLED
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(.text.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    rtc.o(.text.MX_RTC_Init) refers to rtc.o(.bss.hrtc) for hrtc
    rtc.o(.text.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init) for HAL_RTC_Init
    rtc.o(.text.MX_RTC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    rtc.o(.text.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime) for HAL_RTC_SetTime
    rtc.o(.text.MX_RTC_Init) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate) for HAL_RTC_SetDate
    rtc.o(.ARM.exidx.text.MX_RTC_Init) refers to rtc.o(.text.MX_RTC_Init) for [Anonymous Symbol]
    rtc.o(.text.HAL_RTC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    rtc.o(.text.HAL_RTC_MspInit) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    rtc.o(.text.HAL_RTC_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    rtc.o(.ARM.exidx.text.HAL_RTC_MspInit) refers to rtc.o(.text.HAL_RTC_MspInit) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.HAL_RTC_MspDeInit) refers to rtc.o(.text.HAL_RTC_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to memseta.o(.text) for __aeabi_memclr4
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM2_Init) refers to tim.o(.bss.htim2) for htim2
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM2_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM2_Init) refers to tim.o(.text.MX_TIM2_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for HAL_TIM_IC_Init
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for HAL_TIM_SlaveConfigSynchro
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM3_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for HAL_TIM_IC_ConfigChannel
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.text.HAL_TIM_Base_MspInit) refers to tim.o(.bss.hdma_tim3_ch1) for hdma_tim3_ch1
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    tim.o(.text.HAL_TIM_Base_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(.text.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(.text.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    usart.o(.text.MX_USART1_UART_Init) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(.text.HAL_UART_MspInit) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.fputc) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.fputc) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(.ARM.exidx.text.fputc) refers to usart.o(.text.fputc) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32g4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32g4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32g4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32g4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32g4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32g4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32g4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32g4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.SysTick_Handler) refers to stm32g4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32g4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32g4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler) refers to stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler) refers to adc.o(.bss.hdma_adc1) for hdma_adc1
    stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel2_IRQHandler) refers to stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler) refers to adc.o(.bss.hdma_adc2) for hdma_adc2
    stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel3_IRQHandler) refers to stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler) refers to tim.o(.bss.hdma_tim3_ch1) for hdma_tim3_ch1
    stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel4_IRQHandler) refers to stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32g4xx_it.o(.text.USART1_IRQHandler) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32g4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    stm32g4xx_it.o(.text.USART1_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32g4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32g4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32g4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_msp.o(.text.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32g4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Init) refers to adc.o(.text.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Init) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Init) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(.text.HAL_ADC_DeInit) refers to adc.o(.text.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_ConversionStop) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_Disable) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_Disable) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Start) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_Enable) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_Enable) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForConversion) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForEvent) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.ADC_DMAError) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAError) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_GetValue) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_StartSampling) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_StartSampling) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_StopSampling) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_StopSampling) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError) refers to stm32g4xx_hal_adc.o(.text.HAL_ADC_GetError) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_GetValue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_GetValue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_SetValue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_SetValue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Enable) for ADC_Enable
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_DMAError) for ADC_DMAError
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedQueueOverflowCallback) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedQueueOverflowCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_LevelOutOfWindow2Callback) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow2Callback) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_LevelOutOfWindow3Callback) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow3Callback) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EndOfSamplingCallback) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EndOfSamplingCallback) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop_IT) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop_DMA) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_ConversionStop) for ADC_ConversionStop
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc.o(.text.ADC_Disable) for ADC_Disable
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EnableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EnableInjectedQueue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_DisableInjectedQueue) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_DisableInjectedQueue) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_DisableVoltageRegulator) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_DisableVoltageRegulator) for [Anonymous Symbol]
    stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EnterADCDeepPowerDownMode) refers to stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EnterADCDeepPowerDownMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_Init) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(.text.HAL_Init) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(.text.HAL_Init) refers to stm32g4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32g4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_InitTick) refers to stm32g4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32g4xx_hal.o(.text.HAL_InitTick) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal.o(.text.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(.text.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(.text.HAL_InitTick) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32g4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_DeInit) refers to stm32g4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32g4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32g4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_IncTick) refers to stm32g4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32g4xx_hal.o(.text.HAL_IncTick) refers to stm32g4xx_hal.o(.bss.uwTick) for uwTick
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32g4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_GetTick) refers to stm32g4xx_hal.o(.bss.uwTick) for uwTick
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32g4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_Delay) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(.text.HAL_Delay) refers to stm32g4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32g4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32g4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32g4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32g4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32g4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32g4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_CCMSRAMErase) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_CCMSRAMErase) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableMemorySwappingBank) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableMemorySwappingBank) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableMemorySwappingBank) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableMemorySwappingBank) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_TrimmingConfig) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_TrimmingConfig) for [Anonymous Symbol]
    stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableVREFBUF) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableVREFBUF) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOSwitchBooster) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableIOSwitchBooster) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOSwitchBooster) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableIOSwitchBooster) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOSwitchVDD) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableIOSwitchVDD) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOSwitchVDD) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableIOSwitchVDD) for [Anonymous Symbol]
    stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable) refers to stm32g4xx_hal.o(.text.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableLSECSS) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_EnableLSECSS) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableLSECSS) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_DisableLSECSS) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSECSS) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSECSS) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSECSS) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSECSS) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSECSS_IT) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSECSS_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_LSECSS_Callback) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_Callback) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSConfig) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSConfig) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSGetSynchronizationInfo) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSGetSynchronizationInfo) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncOkCallback) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncWarnCallback) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ExpectedSyncCallback) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ErrorCallback) refers to stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(.text.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32g4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase) refers to stm32g4xx_hal_flash_ex.o(.text.FLASH_PageErase) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32g4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data.pFlash) for pFlash
    stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableSecMemProtection) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableSecMemProtection) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableDebugger) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableDebugger) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_DisableDebugger) refers to stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_DisableDebugger) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ramfunc.o(.ARM.exidx.text.HAL_FLASHEx_EnableRunPowerDown) refers to stm32g4xx_hal_flash_ramfunc.o(.text.HAL_FLASHEx_EnableRunPowerDown) for [Anonymous Symbol]
    stm32g4xx_hal_flash_ramfunc.o(.ARM.exidx.text.HAL_FLASHEx_DisableRunPowerDown) refers to stm32g4xx_hal_flash_ramfunc.o(.text.HAL_FLASHEx_DisableRunPowerDown) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32g4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxSync) refers to stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxSync) for [Anonymous Symbol]
    stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxRequestGenerator) refers to stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxRequestGenerator) for [Anonymous Symbol]
    stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_EnableMuxRequestGenerator) refers to stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_EnableMuxRequestGenerator) for [Anonymous Symbol]
    stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_DisableMuxRequestGenerator) refers to stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_DisableMuxRequestGenerator) for [Anonymous Symbol]
    stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MUX_IRQHandler) refers to stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_MUX_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBatteryCharging) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBatteryCharging) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBatteryCharging) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBatteryCharging) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableInternalWakeUpLine) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableInternalWakeUpLine) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableInternalWakeUpLine) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableInternalWakeUpLine) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullUp) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullUp) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullUp) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullUp) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullDown) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullDown) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullDown) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullDown) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePullUpPullDownConfig) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePullUpPullDownConfig) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePullUpPullDownConfig) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePullUpPullDownConfig) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableSRAM2ContentRetention) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableSRAM2ContentRetention) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableSRAM2ContentRetention) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableSRAM2ContentRetention) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM1) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM1) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM1) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM1) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM2) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM2) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM2) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM2) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM3) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM3) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM3) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM3) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM4) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM4) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM4) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM4) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVM) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVM) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableLowPowerRunMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableLowPowerRunMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSTOP0Mode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP0Mode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSTOP1Mode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP1Mode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSHUTDOWNMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSHUTDOWNMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM1Callback) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM1Callback) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM2Callback) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM2Callback) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM3Callback) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM3Callback) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM4Callback) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM4Callback) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableUCPDStandbyMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableUCPDStandbyMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableUCPDStandbyMode) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDStandbyMode) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableUCPDDeadBattery) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableUCPDDeadBattery) for [Anonymous Symbol]
    stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableUCPDDeadBattery) refers to stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDDeadBattery) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32g4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32g4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32g4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32g4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32g4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init) refers to rtc.o(.text.HAL_RTC_MspInit) for HAL_RTC_MspInit
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_Init) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_MspInit) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.RTC_EnterInitMode) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_EnterInitMode) refers to stm32g4xx_hal_rtc.o(.text.RTC_EnterInitMode) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_ExitInitMode) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeInit) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeInit) refers to rtc.o(.text.HAL_RTC_MspDeInit) for HAL_RTC_MspDeInit
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DeInit) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_WaitForSynchro) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_WaitForSynchro) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_WaitForSynchro) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_MspDeInit) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetTime) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_ByteToBcd2) refers to stm32g4xx_hal_rtc.o(.text.RTC_ByteToBcd2) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetTime) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetTime) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_Bcd2ToByte) refers to stm32g4xx_hal_rtc.o(.text.RTC_Bcd2ToByte) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetDate) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetDate) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetDate) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetAlarm) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm_IT) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetAlarm_IT) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeactivateAlarm) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DeactivateAlarm) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeactivateAlarm) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetAlarm) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetAlarm) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmIRQHandler) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmAEventCallback) for HAL_RTC_AlarmAEventCallback
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_AlarmBEventCallback) for HAL_RTCEx_AlarmBEventCallback
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_AlarmIRQHandler) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmIRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_AlarmAEventCallback) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmAEventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.text.HAL_RTC_PollForAlarmAEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_PollForAlarmAEvent) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_PollForAlarmAEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetState) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_Add1Hour) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_Add1Hour) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_Sub1Hour) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_Sub1Hour) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_SetStoreOperation) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_SetStoreOperation) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_ClearStoreOperation) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_ClearStoreOperation) for [Anonymous Symbol]
    stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_ReadStoreOperation) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_ReadStoreOperation) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTimeStamp) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTimeStamp) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTimeStamp_IT) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTimeStamp_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateTimeStamp) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateTimeStamp) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTimeStamp) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTimeStamp) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateInternalTimeStamp) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateInternalTimeStamp) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_GetTimeStamp) refers to stm32g4xx_hal_rtc.o(.text.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_GetTimeStamp) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_GetTimeStamp) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TimeStampEventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampEventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampEventCallback) for HAL_RTCEx_TimeStampEventCallback
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TimeStampIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampIRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTimeStampEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForTimeStampEvent) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTimeStampEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetWakeUpTimer) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer_IT) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetWakeUpTimer_IT) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateWakeUpTimer) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateWakeUpTimer) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_GetWakeUpTimer) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_GetWakeUpTimer) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerEventCallback) for HAL_RTCEx_WakeUpTimerEventCallback
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_WakeUpTimerIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerIRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_WakeUpTimerEventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerEventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForWakeUpTimerEvent) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForWakeUpTimerEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSmoothCalib) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetSmoothCalib) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSmoothCalib) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSynchroShift) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSynchroShift) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_WaitForSynchro) for HAL_RTC_WaitForSynchro
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetSynchroShift) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSynchroShift) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetCalibrationOutPut) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetCalibrationOutPut) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateCalibrationOutPut) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateCalibrationOutPut) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetRefClock) refers to stm32g4xx_hal_rtc.o(.text.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetRefClock) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetRefClock) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetRefClock) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateRefClock) refers to stm32g4xx_hal_rtc.o(.text.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateRefClock) refers to stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateRefClock) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateRefClock) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_EnableBypassShadow) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_EnableBypassShadow) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DisableBypassShadow) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DisableBypassShadow) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_AlarmBEventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_AlarmBEventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForAlarmBEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForAlarmBEvent) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForAlarmBEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTamper) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTamper) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTamper_IT) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTamper_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateTamper) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateTamper) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTamperEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForTamperEvent) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTamperEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTamper) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTamper) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTamper_IT) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTamper_IT) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateInternalTamper) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateInternalTamper) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForInternalTamperEvent) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForInternalTamperEvent) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForInternalTamperEvent) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper5EventCallback) for HAL_RTCEx_InternalTamper5EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper6EventCallback) for HAL_RTCEx_InternalTamper6EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper1EventCallback) for HAL_RTCEx_Tamper1EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper2EventCallback) for HAL_RTCEx_Tamper2EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper3EventCallback) for HAL_RTCEx_Tamper3EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper3EventCallback) for HAL_RTCEx_InternalTamper3EventCallback
    stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper4EventCallback) for HAL_RTCEx_InternalTamper4EventCallback
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TamperIRQHandler) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper1EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper1EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper2EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper2EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper3EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper3EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper3EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper3EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper4EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper4EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper5EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper5EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper6EventCallback) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper6EventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_BKUPWrite) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_BKUPWrite) for [Anonymous Symbol]
    stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_BKUPRead) refers to stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_BKUPRead) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TransitionErrorCallback) for HAL_TIMEx_TransitionErrorCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EncoderIndexCallback) for HAL_TIMEx_EncoderIndexCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DirectionChangeCallback) for HAL_TIMEx_DirectionChangeCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_IndexErrorCallback) for HAL_TIMEx_IndexErrorCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32g4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32g4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32g4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32g4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Start_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Stop_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Stop_DMA
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Start_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Stop_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(.rodata.cst16) for .Lswitch.table.HAL_TIMEx_PWMN_Stop_DMA
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakInput) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakInput) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TISelection) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TISelection) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GroupChannel5) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_GroupChannel5) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisarmBreakInput) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisarmBreakInput) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ReArmBreakInput) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DitheringEnable) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DitheringEnable) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DitheringDisable) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DitheringDisable) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OC_ConfigPulseOnCompare) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OC_ConfigPulseOnCompare) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigSlaveModePreload) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigSlaveModePreload) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableSlaveModePreload) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableSlaveModePreload) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableSlaveModePreload) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableSlaveModePreload) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableDeadTimePreload) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableDeadTimePreload) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableDeadTimePreload) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableDeadTimePreload) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigDeadTime) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigDeadTime) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigAsymmetricalDeadTime) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigAsymmetricalDeadTime) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableAsymmetricalDeadTime) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableAsymmetricalDeadTime) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableAsymmetricalDeadTime) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableAsymmetricalDeadTime) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigEncoderIndex) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigEncoderIndex) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableEncoderIndex) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableEncoderIndex) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableEncoderIndex) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableEncoderIndex) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableEncoderFirstIndex) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableEncoderFirstIndex) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableEncoderFirstIndex) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableEncoderFirstIndex) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_Break2Callback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EncoderIndexCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EncoderIndexCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DirectionChangeCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DirectionChangeCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_IndexErrorCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_IndexErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TransitionErrorCallback) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TransitionErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.rodata.cst16) for .Lswitch.table.UART_SetConfig.4
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.rodata.UARTPrescTable) for UARTPrescTable
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32g4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_AdvFeatureConfig) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_CheckIdleState) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32g4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT_FIFOEN) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT_FIFOEN) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT) refers to stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMAError) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32g4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ReceiverTimeout_Config) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ReceiverTimeout_Config) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_EnableReceiverTimeout) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_EnableReceiverTimeout) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DisableReceiverTimeout) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_DisableReceiverTimeout) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnableMuteMode) refers to stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_DisableMuteMode) refers to stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32g4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT_FIFOEN) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT) refers to stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_RS485Ex_Init) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_WakeupCallback) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_RxFifoFullCallback) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_TxFifoEmptyCallback) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart.o(.text.UART_CheckIdleState) for UART_CheckIdleState
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_StopModeWakeUpSourceConfig) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_EnableStopMode) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_EnableStopMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_DisableStopMode) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableStopMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(.rodata.cst8) for UARTEx_SetNbDataToProcess.numerator
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_EnableFifoMode) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_EnableFifoMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_DisableFifoMode) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(.rodata.cst8) for UARTEx_SetNbDataToProcess.numerator
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_SetTxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(.rodata.cst8) for UARTEx_SetNbDataToProcess.numerator
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_SetRxFifoThreshold) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    system_stm32g4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32g4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32g4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32g4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32g4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.scheduler_run) refers to stm32g4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to led_app.o(.text.led_proc) for led_proc
    scheduler.o(.data.scheduler_task) refers to key_app.o(.text.key_proc) for key_proc
    scheduler.o(.data.scheduler_task) refers to lcd_app.o(.text.lcd_proc) for lcd_proc
    scheduler.o(.data.scheduler_task) refers to uart_app.o(.text.uart_proc) for uart_proc
    scheduler.o(.data.scheduler_task) refers to adc_app.o(.text.adc_proc) for adc_proc
    scheduler.o(.data.scheduler_task) refers to rtc_app.o(.text.rtc_proc) for rtc_proc
    scheduler.o(.data.scheduler_task) refers to tim_app.o(.text.ic_proc) for ic_proc
    key_app.o(.text.key_read) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.ARM.exidx.text.key_read) refers to key_app.o(.text.key_read) for [Anonymous Symbol]
    key_app.o(.text.key_proc) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.text.key_proc) refers to key_app.o(.bss.key_old) for key_old
    key_app.o(.text.key_proc) refers to key_app.o(.bss.key_val) for key_val
    key_app.o(.text.key_proc) refers to key_app.o(.bss.key_down) for key_down
    key_app.o(.text.key_proc) refers to key_app.o(.bss.key_up) for key_up
    key_app.o(.ARM.exidx.text.key_proc) refers to key_app.o(.text.key_proc) for [Anonymous Symbol]
    key_app.o(.text.key_init) refers to key_app.o(.bss.btns) for btns
    key_app.o(.ARM.exidx.text.key_init) refers to key_app.o(.text.key_init) for [Anonymous Symbol]
    key_app.o(.text.key_task) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.text.key_task) refers to led_app.o(.bss.ucLed) for ucLed
    key_app.o(.ARM.exidx.text.key_task) refers to key_app.o(.text.key_task) for [Anonymous Symbol]
    key_app.o(.text.key_state) refers to key_app.o(.bss.btns) for btns
    key_app.o(.text.key_state) refers to key_app.o(.text.key_task) for key_task
    key_app.o(.ARM.exidx.text.key_state) refers to key_app.o(.text.key_state) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Delay_LCD) refers to lcd.o(.text.Delay_LCD) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.REG_8230_Init) refers to lcd.o(.text.REG_8230_Init) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_WriteReg) refers to lcd.o(.text.LCD_WriteReg) for [Anonymous Symbol]
    lcd.o(.text.REG_932X_Init) refers to stm32g4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd.o(.ARM.exidx.text.REG_932X_Init) refers to lcd.o(.text.REG_932X_Init) for [Anonymous Symbol]
    lcd.o(.text.LCD_Init) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.text.LCD_Init) refers to lcd.o(.bss.dummy) for dummy
    lcd.o(.text.LCD_Init) refers to lcd.o(.text.REG_8230_Init) for REG_8230_Init
    lcd.o(.text.LCD_Init) refers to lcd.o(.text.REG_932X_Init) for REG_932X_Init
    lcd.o(.ARM.exidx.text.LCD_Init) refers to lcd.o(.text.LCD_Init) for [Anonymous Symbol]
    lcd.o(.text.LCD_CtrlLinesConfig) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.ARM.exidx.text.LCD_CtrlLinesConfig) refers to lcd.o(.text.LCD_CtrlLinesConfig) for [Anonymous Symbol]
    lcd.o(.text.LCD_ReadReg) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.ARM.exidx.text.LCD_ReadReg) refers to lcd.o(.text.LCD_ReadReg) for [Anonymous Symbol]
    lcd.o(.text.LCD_SetTextColor) refers to lcd.o(.bss.TextColor) for TextColor
    lcd.o(.ARM.exidx.text.LCD_SetTextColor) refers to lcd.o(.text.LCD_SetTextColor) for [Anonymous Symbol]
    lcd.o(.text.LCD_SetBackColor) refers to lcd.o(.data.BackColor) for BackColor
    lcd.o(.ARM.exidx.text.LCD_SetBackColor) refers to lcd.o(.text.LCD_SetBackColor) for [Anonymous Symbol]
    lcd.o(.text.LCD_ClearLine) refers to lcd.o(.rodata.ASCII_Table) for ASCII_Table
    lcd.o(.text.LCD_ClearLine) refers to lcd.o(.text.LCD_DrawChar) for LCD_DrawChar
    lcd.o(.ARM.exidx.text.LCD_ClearLine) refers to lcd.o(.text.LCD_ClearLine) for [Anonymous Symbol]
    lcd.o(.text.LCD_DisplayStringLine) refers to lcd.o(.rodata.ASCII_Table) for ASCII_Table
    lcd.o(.text.LCD_DisplayStringLine) refers to lcd.o(.text.LCD_DrawChar) for LCD_DrawChar
    lcd.o(.ARM.exidx.text.LCD_DisplayStringLine) refers to lcd.o(.text.LCD_DisplayStringLine) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_Clear) refers to lcd.o(.text.LCD_Clear) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_SetCursor) refers to lcd.o(.text.LCD_SetCursor) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_WriteRAM_Prepare) refers to lcd.o(.text.LCD_WriteRAM_Prepare) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawChar) refers to lcd.o(.data.BackColor) for BackColor
    lcd.o(.text.LCD_DrawChar) refers to lcd.o(.bss.TextColor) for TextColor
    lcd.o(.ARM.exidx.text.LCD_DrawChar) refers to lcd.o(.text.LCD_DrawChar) for [Anonymous Symbol]
    lcd.o(.text.LCD_DisplayChar) refers to lcd.o(.rodata.ASCII_Table) for ASCII_Table
    lcd.o(.text.LCD_DisplayChar) refers to lcd.o(.text.LCD_DrawChar) for LCD_DrawChar
    lcd.o(.ARM.exidx.text.LCD_DisplayChar) refers to lcd.o(.text.LCD_DisplayChar) for [Anonymous Symbol]
    lcd.o(.text.LCD_DisplayString) refers to lcd.o(.rodata.ASCII_Table) for ASCII_Table
    lcd.o(.text.LCD_DisplayString) refers to lcd.o(.text.LCD_DrawChar) for LCD_DrawChar
    lcd.o(.ARM.exidx.text.LCD_DisplayString) refers to lcd.o(.text.LCD_DisplayString) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_SetDisplayWindow) refers to lcd.o(.text.LCD_SetDisplayWindow) for [Anonymous Symbol]
    lcd.o(.text.LCD_WindowModeDisable) refers to lcd.o(.text.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    lcd.o(.ARM.exidx.text.LCD_WindowModeDisable) refers to lcd.o(.text.LCD_WindowModeDisable) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawLine) refers to lcd.o(.bss.TextColor) for TextColor
    lcd.o(.ARM.exidx.text.LCD_DrawLine) refers to lcd.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_WriteRAM) refers to lcd.o(.text.LCD_WriteRAM) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawRect) refers to lcd.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd.o(.ARM.exidx.text.LCD_DrawRect) refers to lcd.o(.text.LCD_DrawRect) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawCircle) refers to lcd.o(.bss.TextColor) for TextColor
    lcd.o(.ARM.exidx.text.LCD_DrawCircle) refers to lcd.o(.text.LCD_DrawCircle) for [Anonymous Symbol]
    lcd.o(.text.LCD_DrawMonoPict) refers to lcd.o(.data.BackColor) for BackColor
    lcd.o(.text.LCD_DrawMonoPict) refers to lcd.o(.bss.TextColor) for TextColor
    lcd.o(.ARM.exidx.text.LCD_DrawMonoPict) refers to lcd.o(.text.LCD_DrawMonoPict) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_WriteBMP) refers to lcd.o(.text.LCD_WriteBMP) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_WR_REG) refers to lcd.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    lcd.o(.text.LCD_BusIn) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.ARM.exidx.text.LCD_BusIn) refers to lcd.o(.text.LCD_BusIn) for [Anonymous Symbol]
    lcd.o(.text.LCD_BusOut) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.ARM.exidx.text.LCD_BusOut) refers to lcd.o(.text.LCD_BusOut) for [Anonymous Symbol]
    lcd.o(.text.LCD_ReadRAM) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(.ARM.exidx.text.LCD_ReadRAM) refers to lcd.o(.text.LCD_ReadRAM) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_PowerOn) refers to lcd.o(.text.LCD_PowerOn) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_DisplayOn) refers to lcd.o(.text.LCD_DisplayOn) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_DisplayOff) refers to lcd.o(.text.LCD_DisplayOff) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_DrawPicture) refers to lcd.o(.text.LCD_DrawPicture) for [Anonymous Symbol]
    lcd_app.o(.text.LcdSprintf) refers to printfa.o(i.__0vsprintf) for vsprintf
    lcd_app.o(.text.LcdSprintf) refers to lcd.o(.text.LCD_DisplayStringLine) for LCD_DisplayStringLine
    lcd_app.o(.ARM.exidx.text.LcdSprintf) refers to lcd_app.o(.text.LcdSprintf) for [Anonymous Symbol]
    lcd_app.o(.ARM.exidx.text.lcd_proc) refers to lcd_app.o(.text.lcd_proc) for [Anonymous Symbol]
    led_app.o(.text.led_disp) refers to led_app.o(.data.led_disp.temp_old) for led_disp.temp_old
    led_app.o(.ARM.exidx.text.led_disp) refers to led_app.o(.text.led_disp) for [Anonymous Symbol]
    led_app.o(.text.led_proc) refers to led_app.o(.bss.ucLed) for ucLed
    led_app.o(.text.led_proc) refers to led_app.o(.data.led_disp.temp_old) for led_disp.temp_old
    led_app.o(.ARM.exidx.text.led_proc) refers to led_app.o(.text.led_proc) for [Anonymous Symbol]
    system.o(.ARM.exidx.text.system_init) refers to system.o(.text.system_init) for [Anonymous Symbol]
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.bss.usart_rb) for usart_rb
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.ringbuffer_is_full) for ringbuffer_is_full
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.ringbuffer_write) for ringbuffer_write
    uart_app.o(.text.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to uart_app.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    uart_app.o(.text.uart_proc) refers to uart_app.o(.bss.usart_rb) for usart_rb
    uart_app.o(.text.uart_proc) refers to ringbuffer.o(.text.ringbuffer_is_empty) for ringbuffer_is_empty
    uart_app.o(.text.uart_proc) refers to uart_app.o(.bss.usart_read_buffer) for usart_read_buffer
    uart_app.o(.text.uart_proc) refers to ringbuffer.o(.text.ringbuffer_read) for ringbuffer_read
    uart_app.o(.text.uart_proc) refers to printfa.o(i.__0printf) for __2printf
    uart_app.o(.text.uart_proc) refers to memseta.o(.text) for __aeabi_memclr4
    uart_app.o(.ARM.exidx.text.uart_proc) refers to uart_app.o(.text.uart_proc) for [Anonymous Symbol]
    ringbuffer.o(.text.ringbuffer_init) refers to memseta.o(.text) for __aeabi_memclr4
    ringbuffer.o(.ARM.exidx.text.ringbuffer_init) refers to ringbuffer.o(.text.ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.ringbuffer_is_full) refers to ringbuffer.o(.text.ringbuffer_is_full) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.ringbuffer_is_empty) refers to ringbuffer.o(.text.ringbuffer_is_empty) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.ringbuffer_write) refers to ringbuffer.o(.text.ringbuffer_write) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.ringbuffer_read) refers to ringbuffer.o(.text.ringbuffer_read) for [Anonymous Symbol]
    adc_app.o(.text.adc_proc) refers to adc_app.o(.bss.adc_value) for adc_value
    adc_app.o(.text.adc_proc) refers to adc_app.o(.bss.dma_buff) for dma_buff
    adc_app.o(.ARM.exidx.text.adc_proc) refers to adc_app.o(.text.adc_proc) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.limit_value) refers to filter.o(.text.limit_value) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.compare) refers to filter.o(.text.compare) for [Anonymous Symbol]
    filter.o(.text.mid_value) refers to malloc.o(i.malloc) for malloc
    filter.o(.text.mid_value) refers to memcpya.o(.text) for __aeabi_memcpy4
    filter.o(.text.mid_value) refers to filter.o(.text.compare) for compare
    filter.o(.text.mid_value) refers to qsort.o(.text) for qsort
    filter.o(.text.mid_value) refers to malloc.o(i.free) for free
    filter.o(.ARM.exidx.text.mid_value) refers to filter.o(.text.mid_value) for [Anonymous Symbol]
    filter.o(.ARM.exidx.text.avg_value) refers to filter.o(.text.avg_value) for [Anonymous Symbol]
    filter.o(.text.adc_filter) refers to filter.o(.bss.adc_filter.LastValue) for adc_filter.LastValue
    filter.o(.ARM.exidx.text.adc_filter) refers to filter.o(.text.adc_filter) for [Anonymous Symbol]
    i2c_hal.o(.text.SDA_Input_Mode) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(.ARM.exidx.text.SDA_Input_Mode) refers to i2c_hal.o(.text.SDA_Input_Mode) for [Anonymous Symbol]
    i2c_hal.o(.text.SDA_Output_Mode) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(.ARM.exidx.text.SDA_Output_Mode) refers to i2c_hal.o(.text.SDA_Output_Mode) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.SDA_Output) refers to i2c_hal.o(.text.SDA_Output) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.SCL_Output) refers to i2c_hal.o(.text.SCL_Output) for [Anonymous Symbol]
    i2c_hal.o(.text.SDA_Input) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    i2c_hal.o(.ARM.exidx.text.SDA_Input) refers to i2c_hal.o(.text.SDA_Input) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.I2CStart) refers to i2c_hal.o(.text.I2CStart) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.I2CStop) refers to i2c_hal.o(.text.I2CStop) for [Anonymous Symbol]
    i2c_hal.o(.text.I2CWaitAck) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(.text.I2CWaitAck) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    i2c_hal.o(.ARM.exidx.text.I2CWaitAck) refers to i2c_hal.o(.text.I2CWaitAck) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.I2CSendAck) refers to i2c_hal.o(.text.I2CSendAck) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.I2CSendNotAck) refers to i2c_hal.o(.text.I2CSendNotAck) for [Anonymous Symbol]
    i2c_hal.o(.ARM.exidx.text.I2CSendByte) refers to i2c_hal.o(.text.I2CSendByte) for [Anonymous Symbol]
    i2c_hal.o(.text.I2CReceiveByte) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(.text.I2CReceiveByte) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    i2c_hal.o(.ARM.exidx.text.I2CReceiveByte) refers to i2c_hal.o(.text.I2CReceiveByte) for [Anonymous Symbol]
    i2c_hal.o(.text.I2CInit) refers to stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c_hal.o(.ARM.exidx.text.I2CInit) refers to i2c_hal.o(.text.I2CInit) for [Anonymous Symbol]
    i2c_hal.o(.text.eeprom_write) refers to i2c_hal.o(.text.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(.text.eeprom_write) refers to stm32g4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    i2c_hal.o(.ARM.exidx.text.eeprom_write) refers to i2c_hal.o(.text.eeprom_write) for [Anonymous Symbol]
    i2c_hal.o(.text.eeprom_read) refers to i2c_hal.o(.text.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(.text.eeprom_read) refers to i2c_hal.o(.text.I2CReceiveByte) for I2CReceiveByte
    i2c_hal.o(.ARM.exidx.text.eeprom_read) refers to i2c_hal.o(.text.eeprom_read) for [Anonymous Symbol]
    i2c_hal.o(.text.mcp4017_write) refers to i2c_hal.o(.text.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(.ARM.exidx.text.mcp4017_write) refers to i2c_hal.o(.text.mcp4017_write) for [Anonymous Symbol]
    i2c_hal.o(.text.mcp4017_read) refers to i2c_hal.o(.text.I2CWaitAck) for I2CWaitAck
    i2c_hal.o(.text.mcp4017_read) refers to i2c_hal.o(.text.I2CReceiveByte) for I2CReceiveByte
    i2c_hal.o(.ARM.exidx.text.mcp4017_read) refers to i2c_hal.o(.text.mcp4017_read) for [Anonymous Symbol]
    rtc_app.o(.text.rtc_proc) refers to rtc.o(.bss.hrtc) for hrtc
    rtc_app.o(.text.rtc_proc) refers to rtc_app.o(.bss.time) for time
    rtc_app.o(.text.rtc_proc) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetTime) for HAL_RTC_GetTime
    rtc_app.o(.text.rtc_proc) refers to rtc_app.o(.bss.date) for date
    rtc_app.o(.text.rtc_proc) refers to stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetDate) for HAL_RTC_GetDate
    rtc_app.o(.ARM.exidx.text.rtc_proc) refers to rtc_app.o(.text.rtc_proc) for [Anonymous Symbol]
    tim_app.o(.ARM.exidx.text.pwm_set_duty) refers to tim_app.o(.text.pwm_set_duty) for [Anonymous Symbol]
    tim_app.o(.ARM.exidx.text.pwm_set_frequency) refers to tim_app.o(.text.pwm_set_frequency) for [Anonymous Symbol]
    tim_app.o(.text.ic_proc) refers to tim_app.o(.bss.tim_ic_buffer) for tim_ic_buffer
    tim_app.o(.text.ic_proc) refers to tim_app.o(.bss.tim_ic_temp) for tim_ic_temp
    tim_app.o(.text.ic_proc) refers to tim_app.o(.bss.tim_ic_val) for tim_ic_val
    tim_app.o(.text.ic_proc) refers to filter.o(.text.limit_value) for limit_value
    tim_app.o(.ARM.exidx.text.ic_proc) refers to tim_app.o(.text.ic_proc) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32g431xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32g431xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32g431xx.o(HEAP), (512 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.SystemClock_Config), (122 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC1_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.MX_ADC2_Init), (8 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing adc.o(.text.HAL_ADC_MspDeInit), (106 bytes).
    Removing adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing rtc.o(.text), (0 bytes).
    Removing rtc.o(.ARM.exidx.text.MX_RTC_Init), (8 bytes).
    Removing rtc.o(.ARM.exidx.text.HAL_RTC_MspInit), (8 bytes).
    Removing rtc.o(.text.HAL_RTC_MspDeInit), (42 bytes).
    Removing rtc.o(.ARM.exidx.text.HAL_RTC_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (134 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM2_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (116 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (64 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing usart.o(.bss.uart_rx_index), (2 bytes).
    Removing usart.o(.bss.uart_rx_ticks), (4 bytes).
    Removing usart.o(.bss.uart_rx_buffer), (128 bytes).
    Removing stm32g4xx_it.o(.text), (0 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel1_IRQHandler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel2_IRQHandler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel3_IRQHandler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.DMA1_Channel4_IRQHandler), (8 bytes).
    Removing stm32g4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32g4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text), (0 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Init), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspInit), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_DeInit), (346 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_DeInit), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.ADC_ConversionStop), (218 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_ConversionStop), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.ADC_Disable), (126 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_Disable), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_Start), (226 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_Enable), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop), (60 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForConversion), (298 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForConversion), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_PollForEvent), (234 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_PollForEvent), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_IT), (354 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_IT), (70 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAConvCplt), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAHalfConvCplt), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.ADC_DMAError), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_Stop_DMA), (118 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_GetValue), (6 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetValue), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_StartSampling), (14 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_StartSampling), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_StopSampling), (14 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_StopSampling), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_IRQHandler), (644 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_LevelOutOfWindowCallback), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConvHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_ConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_AnalogWDGConfig), (638 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_AnalogWDGConfig), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_GetState), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetState), (8 bytes).
    Removing stm32g4xx_hal_adc.o(.text.HAL_ADC_GetError), (4 bytes).
    Removing stm32g4xx_hal_adc.o(.ARM.exidx.text.HAL_ADC_GetError), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_Start), (146 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_Start), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_GetValue), (22 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_GetValue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_Calibration_SetValue), (124 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_Calibration_SetValue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart), (206 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop), (76 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedPollForConversion), (252 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedPollForConversion), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStart_IT), (262 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStart_IT), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedStop_IT), (84 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedStop_IT), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStart_DMA), (222 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStart_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeStop_DMA), (236 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeStop_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeGetValue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedGetValue), (46 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedGetValue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConvCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedQueueOverflowCallback), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_LevelOutOfWindow2Callback), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_LevelOutOfWindow3Callback), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EndOfSamplingCallback), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop), (84 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_IT), (92 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop_IT), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularStop_DMA), (126 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularStop_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_RegularMultiModeStop_DMA), (234 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_RegularMultiModeStop_DMA), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_InjectedConfigChannel), (1016 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_InjectedConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_MultiModeConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EnableInjectedQueue), (42 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EnableInjectedQueue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_DisableInjectedQueue), (44 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_DisableInjectedQueue), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_DisableVoltageRegulator), (30 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_DisableVoltageRegulator), (8 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_EnterADCDeepPowerDownMode), (34 bytes).
    Removing stm32g4xx_hal_adc_ex.o(.ARM.exidx.text.HAL_ADCEx_EnterADCDeepPowerDownMode), (8 bytes).
    Removing stm32g4xx_ll_adc.o(.text), (0 bytes).
    Removing stm32g4xx_hal.o(.text), (0 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DeInit), (44 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SetTickFreq), (46 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_CCMSRAMErase), (24 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_CCMSRAMErase), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableMemorySwappingBank), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableMemorySwappingBank), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_VREFBUF_TrimmingConfig), (18 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_VREFBUF_TrimmingConfig), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableVREFBUF), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableVREFBUF), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOSwitchBooster), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOSwitchBooster), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_EnableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_EnableIOSwitchVDD), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_DisableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_DisableIOSwitchVDD), (8 bytes).
    Removing stm32g4xx_hal.o(.text.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (14 bytes).
    Removing stm32g4xx_hal.o(.ARM.exidx.text.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (198 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (86 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (184 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (66 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_EnableLSECSS), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableLSECSS), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_DisableLSECSS), (18 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableLSECSS), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (30 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (1086 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSECSS), (18 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSECSS), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSECSS), (26 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSECSS), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSECSS_IT), (50 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSECSS_IT), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_IRQHandler), (30 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_LSECSS_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_LSECSS_Callback), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnableLSCO), (160 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnableLSCO), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisableLSCO), (96 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisableLSCO), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSConfig), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (18 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSGetSynchronizationInfo), (42 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSGetSynchronizationInfo), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRSWaitSynchronization), (150 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRSWaitSynchronization), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_IRQHandler), (98 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncOkCallback), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_SyncWarnCallback), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ExpectedSyncCallback), (8 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_CRS_ErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program), (330 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (88 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (250 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (242 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_Lock), (24 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (46 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (30 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (94 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32g4xx_hal_flash.o(.data.pFlash), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (340 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.FLASH_PageErase), (40 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (186 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (760 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (180 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableSecMemProtection), (20 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableSecMemProtection), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_EnableDebugger), (18 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_EnableDebugger), (8 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.text.HAL_FLASHEx_DisableDebugger), (18 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_DisableDebugger), (8 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.text.HAL_FLASHEx_EnableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.ARM.exidx.text.HAL_FLASHEx_EnableRunPowerDown), (8 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.text.HAL_FLASHEx_DisableRunPowerDown), (40 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.ARM.exidx.text.HAL_FLASHEx_DisableRunPowerDown), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (264 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (216 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (174 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (146 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (56 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (30 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32g4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (30 bytes).
    Removing stm32g4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_DeInit), (252 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_Start), (148 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (264 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (52 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32g4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxSync), (86 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxSync), (8 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_ConfigMuxRequestGenerator), (64 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ConfigMuxRequestGenerator), (8 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_EnableMuxRequestGenerator), (8 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_DisableMuxRequestGenerator), (8 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.text.HAL_DMAEx_MUX_IRQHandler), (94 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MUX_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (134 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (30 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (64 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (38 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32g4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (38 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBatteryCharging), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBatteryCharging), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBatteryCharging), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableInternalWakeUpLine), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableInternalWakeUpLine), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableInternalWakeUpLine), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableInternalWakeUpLine), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullUp), (224 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullUp), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullUp), (182 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullUp), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableGPIOPullDown), (228 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableGPIOPullDown), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableGPIOPullDown), (176 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableGPIOPullDown), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePullUpPullDownConfig), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePullUpPullDownConfig), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePullUpPullDownConfig), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePullUpPullDownConfig), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableSRAM2ContentRetention), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableSRAM2ContentRetention), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableSRAM2ContentRetention), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableSRAM2ContentRetention), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM1), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM1), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM1), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM1), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM2), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM2), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM2), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM2), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM3), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM3), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM3), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM3), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnablePVM4), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnablePVM4), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisablePVM4), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisablePVM4), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ConfigPVM), (552 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ConfigPVM), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableLowPowerRunMode), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableLowPowerRunMode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableLowPowerRunMode), (74 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableLowPowerRunMode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP0Mode), (54 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSTOP0Mode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSTOP1Mode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSTOP1Mode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterSHUTDOWNMode), (38 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterSHUTDOWNMode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVD_PVM_IRQHandler), (90 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVD_PVM_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM1Callback), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM2Callback), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM3Callback), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_PVM4Callback), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableUCPDStandbyMode), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableUCPDStandbyMode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDStandbyMode), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableUCPDStandbyMode), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableUCPDDeadBattery), (18 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableUCPDDeadBattery), (8 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableUCPDDeadBattery), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.__NVIC_SystemReset), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_MPU_Enable), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_MPU_Disable), (18 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32g4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32g4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text), (0 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_Init), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_MspInit), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.RTC_EnterInitMode), (82 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_EnterInitMode), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_ExitInitMode), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeInit), (268 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DeInit), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_WaitForSynchro), (50 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_WaitForSynchro), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetTime), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.RTC_ByteToBcd2), (36 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_ByteToBcd2), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetTime), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.RTC_Bcd2ToByte), (18 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.RTC_Bcd2ToByte), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetDate), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetDate), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm), (454 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetAlarm), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetAlarm_IT), (496 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_SetAlarm_IT), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DeactivateAlarm), (166 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DeactivateAlarm), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetAlarm), (196 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetAlarm), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmIRQHandler), (76 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_AlarmIRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_AlarmAEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_AlarmAEventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_PollForAlarmAEvent), (84 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_PollForAlarmAEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetState), (6 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_GetState), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_Add1Hour), (34 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_Add1Hour), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_Sub1Hour), (34 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_Sub1Hour), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_SetStoreOperation), (34 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_SetStoreOperation), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_ClearStoreOperation), (34 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_ClearStoreOperation), (8 bytes).
    Removing stm32g4xx_hal_rtc.o(.text.HAL_RTC_DST_ReadStoreOperation), (16 bytes).
    Removing stm32g4xx_hal_rtc.o(.ARM.exidx.text.HAL_RTC_DST_ReadStoreOperation), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTimeStamp), (76 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTimeStamp), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTimeStamp_IT), (108 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTimeStamp_IT), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateTimeStamp), (84 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateTimeStamp), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTimeStamp), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTimeStamp), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateInternalTimeStamp), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateInternalTimeStamp), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_GetTimeStamp), (142 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_GetTimeStamp), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TimeStampEventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TimeStampIRQHandler), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TimeStampIRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTimeStampEvent), (104 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForTimeStampEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer), (152 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetWakeUpTimer), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetWakeUpTimer_IT), (176 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetWakeUpTimer_IT), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateWakeUpTimer), (126 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateWakeUpTimer), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_GetWakeUpTimer), (14 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_GetWakeUpTimer), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerIRQHandler), (50 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_WakeUpTimerIRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_WakeUpTimerEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_WakeUpTimerEventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForWakeUpTimerEvent), (86 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForWakeUpTimerEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSmoothCalib), (136 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetSmoothCalib), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetSynchroShift), (154 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetSynchroShift), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetCalibrationOutPut), (70 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetCalibrationOutPut), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateCalibrationOutPut), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateCalibrationOutPut), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetRefClock), (92 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetRefClock), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateRefClock), (92 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateRefClock), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_EnableBypassShadow), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_EnableBypassShadow), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DisableBypassShadow), (64 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DisableBypassShadow), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_AlarmBEventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_AlarmBEventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForAlarmBEvent), (86 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForAlarmBEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTamper), (158 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTamper), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetTamper_IT), (208 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetTamper_IT), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateTamper), (56 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateTamper), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForTamperEvent), (88 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForTamperEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTamper), (76 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTamper), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_SetInternalTamper_IT), (114 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_SetInternalTamper_IT), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_DeactivateInternalTamper), (40 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_DeactivateInternalTamper), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_PollForInternalTamperEvent), (88 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_PollForInternalTamperEvent), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_TamperIRQHandler), (134 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_TamperIRQHandler), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper1EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper1EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper2EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper2EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_Tamper3EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_Tamper3EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper3EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper3EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper4EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper4EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper5EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper5EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_InternalTamper6EventCallback), (2 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_InternalTamper6EventCallback), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_BKUPWrite), (14 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_BKUPWrite), (8 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.text.HAL_RTCEx_BKUPRead), (14 bytes).
    Removing stm32g4xx_hal_rtc_ex.o(.ARM.exidx.text.HAL_RTCEx_BKUPRead), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (104 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (138 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (42 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (146 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (50 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (216 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (60 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (98 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (104 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (328 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (202 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (358 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (232 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (658 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (104 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (202 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (358 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (232 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (658 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (236 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (104 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (324 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (182 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (354 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (212 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (214 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (150 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (148 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (166 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (164 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (182 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (202 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (260 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (570 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (266 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (512 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (806 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (142 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (134 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (610 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (458 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (458 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (646 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (74 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (22 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32g4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32g4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (190 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (206 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (252 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (214 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (252 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (538 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (66 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (162 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (214 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (252 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (184 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (538 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (162 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (124 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (118 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (140 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (134 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (182 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (182 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (222 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakInput), (196 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakInput), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (34 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TISelection), (134 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TISelection), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_GroupChannel5), (48 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GroupChannel5), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisarmBreakInput), (76 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisarmBreakInput), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ReArmBreakInput), (90 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ReArmBreakInput), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DitheringEnable), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DitheringEnable), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DitheringDisable), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DitheringDisable), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_OC_ConfigPulseOnCompare), (56 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OC_ConfigPulseOnCompare), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigSlaveModePreload), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigSlaveModePreload), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableSlaveModePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableSlaveModePreload), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableSlaveModePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableSlaveModePreload), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableDeadTimePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableDeadTimePreload), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableDeadTimePreload), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableDeadTimePreload), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigDeadTime), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigDeadTime), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigAsymmetricalDeadTime), (16 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigAsymmetricalDeadTime), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableAsymmetricalDeadTime), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableAsymmetricalDeadTime), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableAsymmetricalDeadTime), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableAsymmetricalDeadTime), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigEncoderIndex), (82 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigEncoderIndex), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableEncoderIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableEncoderIndex), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableEncoderIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableEncoderIndex), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EnableEncoderFirstIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EnableEncoderFirstIndex), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DisableEncoderFirstIndex), (14 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DisableEncoderFirstIndex), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_Break2Callback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_EncoderIndexCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_EncoderIndexCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_DirectionChangeCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_DirectionChangeCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_IndexErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_IndexErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_TransitionErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_TransitionErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (30 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32g4xx_hal_tim_ex.o(.rodata.cst16), (48 bytes).
    Removing stm32g4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_AdvFeatureConfig), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_CheckIdleState), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (106 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_LIN_Init), (140 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (130 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_DeInit), (62 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Receive), (252 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (176 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT_FIFOEN), (100 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT_FIFOEN), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT_FIFOEN), (96 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT_FIFOEN), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_TxISR_16BIT), (64 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_16BIT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_TxISR_8BIT), (60 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_TxISR_8BIT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (98 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_Start_Receive_IT), (220 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (180 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMATransmitCplt), (40 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (212 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_DMAPause), (96 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_DMAResume), (84 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_DMAStop), (184 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Abort), (190 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (104 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (120 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (228 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (72 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (62 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (116 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (36 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (138 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (34 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_ReceiverTimeout_Config), (28 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ReceiverTimeout_Config), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_EnableReceiverTimeout), (68 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_EnableReceiverTimeout), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_DisableReceiverTimeout), (68 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DisableReceiverTimeout), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_EnableMuteMode), (44 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnableMuteMode), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_DisableMuteMode), (44 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_DisableMuteMode), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (52 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (52 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (44 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_GetState), (12 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.HAL_UART_GetError), (6 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT_FIFOEN), (336 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT_FIFOEN), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT_FIFOEN), (340 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT_FIFOEN), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_RxISR_16BIT), (122 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_16BIT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.text.UART_RxISR_8BIT), (126 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_RxISR_8BIT), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32g4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text), (0 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_RS485Ex_Init), (130 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_RS485Ex_Init), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_WakeupCallback), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_RxFifoFullCallback), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_TxFifoEmptyCallback), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_MultiProcessorEx_AddressLength_Set), (46 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_MultiProcessorEx_AddressLength_Set), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_StopModeWakeUpSourceConfig), (134 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_StopModeWakeUpSourceConfig), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_EnableStopMode), (32 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_EnableStopMode), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableStopMode), (32 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_DisableStopMode), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_EnableFifoMode), (122 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_EnableFifoMode), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_DisableFifoMode), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_SetTxFifoThreshold), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_SetRxFifoThreshold), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle), (318 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (84 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32g4xx_hal_uart_ex.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing system_stm32g4xx.o(.text), (0 bytes).
    Removing system_stm32g4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32g4xx.o(.text.SystemCoreClockUpdate), (150 bytes).
    Removing system_stm32g4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing key_app.o(.text), (0 bytes).
    Removing key_app.o(.text.key_read), (76 bytes).
    Removing key_app.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.key_proc), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.key_init), (8 bytes).
    Removing key_app.o(.text.key_task), (214 bytes).
    Removing key_app.o(.ARM.exidx.text.key_task), (8 bytes).
    Removing key_app.o(.text.key_state), (26 bytes).
    Removing key_app.o(.ARM.exidx.text.key_state), (8 bytes).
    Removing lcd.o(.text), (0 bytes).
    Removing lcd.o(.text.Delay_LCD), (2 bytes).
    Removing lcd.o(.ARM.exidx.text.Delay_LCD), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.REG_8230_Init), (8 bytes).
    Removing lcd.o(.text.LCD_WriteReg), (98 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WriteReg), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.REG_932X_Init), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Init), (8 bytes).
    Removing lcd.o(.text.LCD_CtrlLinesConfig), (182 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_CtrlLinesConfig), (8 bytes).
    Removing lcd.o(.text.LCD_ReadReg), (196 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ReadReg), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_SetTextColor), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_SetBackColor), (8 bytes).
    Removing lcd.o(.text.LCD_ClearLine), (88 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ClearLine), (8 bytes).
    Removing lcd.o(.text.LCD_DisplayStringLine), (76 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DisplayStringLine), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Clear), (8 bytes).
    Removing lcd.o(.text.LCD_SetCursor), (190 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_SetCursor), (8 bytes).
    Removing lcd.o(.text.LCD_WriteRAM_Prepare), (64 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WriteRAM_Prepare), (8 bytes).
    Removing lcd.o(.text.LCD_DrawChar), (532 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawChar), (8 bytes).
    Removing lcd.o(.text.LCD_DisplayChar), (26 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DisplayChar), (8 bytes).
    Removing lcd.o(.text.LCD_DisplayString), (90 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DisplayString), (8 bytes).
    Removing lcd.o(.text.LCD_SetDisplayWindow), (1004 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_SetDisplayWindow), (8 bytes).
    Removing lcd.o(.text.LCD_WindowModeDisable), (122 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WindowModeDisable), (8 bytes).
    Removing lcd.o(.text.LCD_DrawLine), (856 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing lcd.o(.text.LCD_WriteRAM), (54 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WriteRAM), (8 bytes).
    Removing lcd.o(.text.LCD_DrawRect), (68 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawRect), (8 bytes).
    Removing lcd.o(.text.LCD_DrawCircle), (2318 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawCircle), (8 bytes).
    Removing lcd.o(.text.LCD_DrawMonoPict), (342 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawMonoPict), (8 bytes).
    Removing lcd.o(.text.LCD_WriteBMP), (346 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WriteBMP), (8 bytes).
    Removing lcd.o(.text.LCD_WR_REG), (62 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing lcd.o(.text.LCD_BusIn), (40 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_BusIn), (8 bytes).
    Removing lcd.o(.text.LCD_BusOut), (42 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_BusOut), (8 bytes).
    Removing lcd.o(.text.LCD_ReadRAM), (198 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_ReadRAM), (8 bytes).
    Removing lcd.o(.text.LCD_PowerOn), (930 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_PowerOn), (8 bytes).
    Removing lcd.o(.text.LCD_DisplayOn), (104 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DisplayOn), (8 bytes).
    Removing lcd.o(.text.LCD_DisplayOff), (102 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DisplayOff), (8 bytes).
    Removing lcd.o(.text.LCD_DrawPicture), (418 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_DrawPicture), (8 bytes).
    Removing lcd.o(.rodata.ASCII_Table), (4560 bytes).
    Removing lcd_app.o(.text), (0 bytes).
    Removing lcd_app.o(.text.LcdSprintf), (44 bytes).
    Removing lcd_app.o(.ARM.exidx.text.LcdSprintf), (8 bytes).
    Removing lcd_app.o(.ARM.exidx.text.lcd_proc), (8 bytes).
    Removing led_app.o(.text), (0 bytes).
    Removing led_app.o(.text.led_disp), (94 bytes).
    Removing led_app.o(.ARM.exidx.text.led_disp), (8 bytes).
    Removing led_app.o(.ARM.exidx.text.led_proc), (8 bytes).
    Removing system.o(.text), (0 bytes).
    Removing system.o(.ARM.exidx.text.system_init), (8 bytes).
    Removing uart_app.o(.text), (0 bytes).
    Removing uart_app.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.uart_proc), (8 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.ringbuffer_is_full), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.ringbuffer_is_empty), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.ringbuffer_write), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.ringbuffer_read), (8 bytes).
    Removing adc_app.o(.text), (0 bytes).
    Removing adc_app.o(.ARM.exidx.text.adc_proc), (8 bytes).
    Removing filter.o(.text), (0 bytes).
    Removing filter.o(.ARM.exidx.text.limit_value), (8 bytes).
    Removing filter.o(.text.compare), (8 bytes).
    Removing filter.o(.ARM.exidx.text.compare), (8 bytes).
    Removing filter.o(.text.mid_value), (92 bytes).
    Removing filter.o(.ARM.exidx.text.mid_value), (8 bytes).
    Removing filter.o(.text.avg_value), (36 bytes).
    Removing filter.o(.ARM.exidx.text.avg_value), (8 bytes).
    Removing filter.o(.text.adc_filter), (26 bytes).
    Removing filter.o(.ARM.exidx.text.adc_filter), (8 bytes).
    Removing filter.o(.bss.adc_filter.LastValue), (4 bytes).
    Removing i2c_hal.o(.text), (0 bytes).
    Removing i2c_hal.o(.text.SDA_Input_Mode), (40 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.SDA_Input_Mode), (8 bytes).
    Removing i2c_hal.o(.text.SDA_Output_Mode), (40 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.SDA_Output_Mode), (8 bytes).
    Removing i2c_hal.o(.text.SDA_Output), (24 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.SDA_Output), (8 bytes).
    Removing i2c_hal.o(.text.SCL_Output), (24 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.SCL_Output), (8 bytes).
    Removing i2c_hal.o(.text.SDA_Input), (26 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.SDA_Input), (8 bytes).
    Removing i2c_hal.o(.text.I2CStart), (42 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CStart), (8 bytes).
    Removing i2c_hal.o(.text.I2CStop), (42 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CStop), (8 bytes).
    Removing i2c_hal.o(.text.I2CWaitAck), (172 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CWaitAck), (8 bytes).
    Removing i2c_hal.o(.text.I2CSendAck), (38 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CSendAck), (8 bytes).
    Removing i2c_hal.o(.text.I2CSendNotAck), (34 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CSendNotAck), (8 bytes).
    Removing i2c_hal.o(.text.I2CSendByte), (78 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CSendByte), (8 bytes).
    Removing i2c_hal.o(.text.I2CReceiveByte), (132 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CReceiveByte), (8 bytes).
    Removing i2c_hal.o(.text.I2CInit), (40 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.I2CInit), (8 bytes).
    Removing i2c_hal.o(.text.eeprom_write), (326 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.eeprom_write), (8 bytes).
    Removing i2c_hal.o(.text.eeprom_read), (402 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.eeprom_read), (8 bytes).
    Removing i2c_hal.o(.text.mcp4017_write), (226 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.mcp4017_write), (8 bytes).
    Removing i2c_hal.o(.text.mcp4017_read), (170 bytes).
    Removing i2c_hal.o(.ARM.exidx.text.mcp4017_read), (8 bytes).
    Removing rtc_app.o(.text), (0 bytes).
    Removing rtc_app.o(.ARM.exidx.text.rtc_proc), (8 bytes).
    Removing tim_app.o(.text), (0 bytes).
    Removing tim_app.o(.text.pwm_set_duty), (44 bytes).
    Removing tim_app.o(.ARM.exidx.text.pwm_set_duty), (8 bytes).
    Removing tim_app.o(.text.pwm_set_frequency), (74 bytes).
    Removing tim_app.o(.ARM.exidx.text.pwm_set_frequency), (8 bytes).
    Removing tim_app.o(.ARM.exidx.text.ic_proc), (8 bytes).

1319 unused section(s) (total 71740 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stdlib/qsort.c          0x00000000   Number         0  qsort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    adc_app.c                                0x00000000   Number         0  adc_app.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    filter.c                                 0x00000000   Number         0  filter.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    i2c_hal.c                                0x00000000   Number         0  i2c_hal.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    key_app.c                                0x00000000   Number         0  key_app.o ABSOLUTE
    lcd.c                                    0x00000000   Number         0  lcd.o ABSOLUTE
    lcd_app.c                                0x00000000   Number         0  lcd_app.o ABSOLUTE
    led_app.c                                0x00000000   Number         0  led_app.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    rtc.c                                    0x00000000   Number         0  rtc.o ABSOLUTE
    rtc_app.c                                0x00000000   Number         0  rtc_app.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_stm32g431xx.s                    0x00000000   Number         0  startup_stm32g431xx.o ABSOLUTE
    stm32g4xx_hal.c                          0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    stm32g4xx_hal_adc.c                      0x00000000   Number         0  stm32g4xx_hal_adc.o ABSOLUTE
    stm32g4xx_hal_adc_ex.c                   0x00000000   Number         0  stm32g4xx_hal_adc_ex.o ABSOLUTE
    stm32g4xx_hal_cortex.c                   0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    stm32g4xx_hal_dma.c                      0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    stm32g4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    stm32g4xx_hal_exti.c                     0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    stm32g4xx_hal_flash.c                    0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    stm32g4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    stm32g4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32g4xx_hal_gpio.c                     0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    stm32g4xx_hal_msp.c                      0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    stm32g4xx_hal_pwr.c                      0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    stm32g4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    stm32g4xx_hal_rcc.c                      0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    stm32g4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    stm32g4xx_hal_rtc.c                      0x00000000   Number         0  stm32g4xx_hal_rtc.o ABSOLUTE
    stm32g4xx_hal_rtc_ex.c                   0x00000000   Number         0  stm32g4xx_hal_rtc_ex.o ABSOLUTE
    stm32g4xx_hal_tim.c                      0x00000000   Number         0  stm32g4xx_hal_tim.o ABSOLUTE
    stm32g4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32g4xx_hal_tim_ex.o ABSOLUTE
    stm32g4xx_hal_uart.c                     0x00000000   Number         0  stm32g4xx_hal_uart.o ABSOLUTE
    stm32g4xx_hal_uart_ex.c                  0x00000000   Number         0  stm32g4xx_hal_uart_ex.o ABSOLUTE
    stm32g4xx_it.c                           0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    stm32g4xx_ll_adc.c                       0x00000000   Number         0  stm32g4xx_ll_adc.o ABSOLUTE
    system.c                                 0x00000000   Number         0  system.o ABSOLUTE
    system_stm32g4xx.c                       0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    tim_app.c                                0x00000000   Number         0  tim_app.o ABSOLUTE
    uart_app.c                               0x00000000   Number         0  uart_app.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g431xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001d8   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001d8   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001dc   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001e0   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001e0   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001e0   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080001e8   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080001e8   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001e8   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001e8   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x080001ec   Number         0  startup_stm32g431xx.o(.text)
    .text                                    0x080001ec   Section       36  startup_stm32g431xx.o(.text)
    .text                                    0x08000210   Section        0  uldiv.o(.text)
    .text                                    0x08000272   Section        0  memseta.o(.text)
    .text                                    0x08000296   Section        0  uidiv.o(.text)
    .text                                    0x080002c2   Section        0  llshl.o(.text)
    .text                                    0x080002e0   Section        0  llushr.o(.text)
    .text                                    0x08000300   Section        0  iusefp.o(.text)
    .text                                    0x08000300   Section        0  dadd.o(.text)
    .text                                    0x0800044e   Section        0  dmul.o(.text)
    .text                                    0x08000532   Section        0  ddiv.o(.text)
    .text                                    0x08000610   Section        0  dfixul.o(.text)
    .text                                    0x08000640   Section       48  cdrcmple.o(.text)
    .text                                    0x08000670   Section       48  init.o(.text)
    .text                                    0x080006a0   Section        0  llsshr.o(.text)
    .text                                    0x080006c4   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x08000780   Section        0  stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    [Anonymous Symbol]                       0x080007e0   Section        0  stm32g4xx_hal_adc.o(.text.ADC_DMAError)
    [Anonymous Symbol]                       0x080007f8   Section        0  stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    [Anonymous Symbol]                       0x08000800   Section        0  stm32g4xx_hal_adc.o(.text.ADC_Enable)
    [Anonymous Symbol]                       0x0800087c   Section        0  stm32g4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000880   Section        0  stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler)
    [Anonymous Symbol]                       0x0800088c   Section        0  stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler)
    [Anonymous Symbol]                       0x08000898   Section        0  stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler)
    [Anonymous Symbol]                       0x080008a4   Section        0  stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler)
    [Anonymous Symbol]                       0x080008b0   Section        0  stm32g4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x080008b4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080008c0   Section        0  stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel)
    [Anonymous Symbol]                       0x08000984   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    [Anonymous Symbol]                       0x08000cb8   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback)
    [Anonymous Symbol]                       0x08000cbc   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback)
    [Anonymous Symbol]                       0x08000cc0   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    [Anonymous Symbol]                       0x08000cc4   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_Init)
    [Anonymous Symbol]                       0x08000ec4   Section        0  adc.o(.text.HAL_ADC_MspInit)
    [Anonymous Symbol]                       0x08001060   Section        0  stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    [Anonymous Symbol]                       0x08001150   Section        0  stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x080011c0   Section        0  stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08001240   Section        0  stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x080012dc   Section        0  stm32g4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08001400   Section        0  stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x080014e4   Section        0  stm32g4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x0800150c   Section        0  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080016b4   Section        0  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x080016c0   Section        0  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x080016cc   Section        0  stm32g4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x080016d8   Section        0  stm32g4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080016f4   Section        0  stm32g4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001714   Section        0  stm32g4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001770   Section        0  stm32g4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x080017a4   Section        0  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080017c8   Section        0  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08001820   Section        0  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001840   Section        0  stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    [Anonymous Symbol]                       0x08001934   Section        0  stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDDeadBattery)
    [Anonymous Symbol]                       0x08001948   Section        0  stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    [Anonymous Symbol]                       0x08001bfc   Section        0  stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001f10   Section        0  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08001f3c   Section        0  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001f68   Section        0  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001fec   Section        0  stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08002480   Section        0  stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetDate)
    [Anonymous Symbol]                       0x080024ec   Section        0  stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetTime)
    [Anonymous Symbol]                       0x08002564   Section        0  stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init)
    [Anonymous Symbol]                       0x08002644   Section        0  rtc.o(.text.HAL_RTC_MspInit)
    [Anonymous Symbol]                       0x080026a4   Section        0  stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate)
    [Anonymous Symbol]                       0x080027f4   Section        0  stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime)
    [Anonymous Symbol]                       0x0800297c   Section        0  stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x080029a8   Section        0  stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x08002a70   Section        0  stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08002b1c   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08002b80   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08002c84   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08002e68   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback)
    [Anonymous Symbol]                       0x08002e6c   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    [Anonymous Symbol]                       0x08002e70   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback)
    [Anonymous Symbol]                       0x08002e74   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    [Anonymous Symbol]                       0x08002fdc   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    [Anonymous Symbol]                       0x08003040   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit)
    [Anonymous Symbol]                       0x08003044   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA)
    [Anonymous Symbol]                       0x08003294   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x0800364c   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x080036b0   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x080036b4   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    [Anonymous Symbol]                       0x080037fc   Section        0  stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro)
    [Anonymous Symbol]                       0x08003848   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode)
    [Anonymous Symbol]                       0x08003880   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x080038d4   Section        0  uart_app.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x0800390c   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback)
    [Anonymous Symbol]                       0x08003910   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold)
    [Anonymous Symbol]                       0x080039a4   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold)
    [Anonymous Symbol]                       0x08003a38   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback)
    [Anonymous Symbol]                       0x08003a3c   Section        0  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback)
    [Anonymous Symbol]                       0x08003a40   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08003a44   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08003d2c   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08003d90   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08003e68   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08003e6c   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x08003e70   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08003f48   Section        0  stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08003f4c   Section        0  stm32g4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08003f50   Section        0  lcd.o(.text.LCD_Clear)
    [Anonymous Symbol]                       0x08004074   Section        0  lcd.o(.text.LCD_Init)
    [Anonymous Symbol]                       0x080042b4   Section        0  lcd.o(.text.LCD_SetBackColor)
    [Anonymous Symbol]                       0x080042cc   Section        0  lcd.o(.text.LCD_SetTextColor)
    [Anonymous Symbol]                       0x080042e4   Section        0  adc.o(.text.MX_ADC1_Init)
    [Anonymous Symbol]                       0x08004394   Section        0  adc.o(.text.MX_ADC2_Init)
    [Anonymous Symbol]                       0x08004428   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x080044a0   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x080045ec   Section        0  rtc.o(.text.MX_RTC_Init)
    [Anonymous Symbol]                       0x08004688   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08004810   Section        0  tim.o(.text.MX_TIM2_Init)
    [Anonymous Symbol]                       0x08004950   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08004a3c   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08004ae4   Section        0  stm32g4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08004ae8   Section        0  stm32g4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08004aec   Section        0  stm32g4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08004af0   Section        0  lcd.o(.text.REG_8230_Init)
    [Anonymous Symbol]                       0x08005564   Section        0  lcd.o(.text.REG_932X_Init)
    [Anonymous Symbol]                       0x080069a4   Section        0  stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode)
    [Anonymous Symbol]                       0x08006a3c   Section        0  stm32g4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08006a40   Section        0  stm32g4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08006a44   Section        0  system_stm32g4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08006a58   Section        0  stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x08006b60   Section        0  stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt)
    [Anonymous Symbol]                       0x08006bd4   Section        0  stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt)
    [Anonymous Symbol]                       0x08006c0c   Section        0  stm32g4xx_hal_tim.o(.text.TIM_DMAError)
    TIM_SlaveTimer_SetConfig                 0x08006c69   Thumb Code   234  stm32g4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig)
    [Anonymous Symbol]                       0x08006c68   Section        0  stm32g4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig)
    [Anonymous Symbol]                       0x08006d54   Section        0  stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig)
    [Anonymous Symbol]                       0x08006e3c   Section        0  stm32g4xx_hal_uart.o(.text.UART_CheckIdleState)
    UART_DMAAbortOnError                     0x08006f05   Thumb Code    16  stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08006f04   Section        0  stm32g4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x08006f15   Thumb Code   136  stm32g4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08006f14   Section        0  stm32g4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x08006f9d   Thumb Code    80  stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x08006f9c   Section        0  stm32g4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08006fed   Thumb Code    22  stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08006fec   Section        0  stm32g4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08007004   Section        0  stm32g4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08007298   Section        0  stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    [Anonymous Symbol]                       0x0800731c   Section        0  stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout)
    [Anonymous Symbol]                       0x080073d0   Section        0  stm32g4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08007404   Section        0  stm32g4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08007408   Section        0  adc_app.o(.text.adc_proc)
    [Anonymous Symbol]                       0x0800747c   Section        0  usart.o(.text.fputc)
    [Anonymous Symbol]                       0x0800749c   Section        0  tim_app.o(.text.ic_proc)
    [Anonymous Symbol]                       0x080074fc   Section        0  key_app.o(.text.key_init)
    [Anonymous Symbol]                       0x0800753c   Section        0  key_app.o(.text.key_proc)
    [Anonymous Symbol]                       0x080075b8   Section        0  lcd_app.o(.text.lcd_proc)
    [Anonymous Symbol]                       0x080075bc   Section        0  led_app.o(.text.led_proc)
    [Anonymous Symbol]                       0x08007624   Section        0  filter.o(.text.limit_value)
    [Anonymous Symbol]                       0x08007658   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x0800778c   Section        0  ringbuffer.o(.text.ringbuffer_init)
    [Anonymous Symbol]                       0x0800779c   Section        0  ringbuffer.o(.text.ringbuffer_is_empty)
    [Anonymous Symbol]                       0x080077a8   Section        0  ringbuffer.o(.text.ringbuffer_is_full)
    [Anonymous Symbol]                       0x080077b4   Section        0  ringbuffer.o(.text.ringbuffer_read)
    [Anonymous Symbol]                       0x08007810   Section        0  ringbuffer.o(.text.ringbuffer_write)
    [Anonymous Symbol]                       0x08007870   Section        0  rtc_app.o(.text.rtc_proc)
    [Anonymous Symbol]                       0x080078a0   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x080078b0   Section        0  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x080078fc   Section        0  system.o(.text.system_init)
    [Anonymous Symbol]                       0x0800792c   Section        0  uart_app.o(.text.uart_proc)
    i.__0printf                              0x08007980   Section        0  printfa.o(i.__0printf)
    i.__scatterload_copy                     0x080079a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080079ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080079b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x080079c1   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x080079c0   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x08007b45   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08007b44   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x08008221   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x08008220   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x08008245   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x08008244   Section        0  printfa.o(i._printf_pre_padding)
    .Lswitch.table.UART_SetConfig.4          0x080082a4   Data          16  stm32g4xx_hal_uart.o(.rodata.cst16)
    UARTEx_SetNbDataToProcess.numerator      0x080082b4   Data           8  stm32g4xx_hal_uart_ex.o(.rodata.cst8)
    [Anonymous Symbol]                       0x080082b4   Section        0  stm32g4xx_hal_uart_ex.o(.rodata.cst8)
    UARTEx_SetNbDataToProcess.denominator    0x080082bc   Data           8  stm32g4xx_hal_uart_ex.o(.rodata.cst8)
    .data                                    0x20000000   Section        4  stdout.o(.data)
    BackColor                                0x20000004   Data           2  lcd.o(.data.BackColor)
    [Anonymous Symbol]                       0x20000004   Section        0  lcd.o(.data.BackColor)
    led_disp.temp_old                        0x2000000c   Data           1  led_app.o(.data.led_disp.temp_old)
    [Anonymous Symbol]                       0x2000000c   Section        0  led_app.o(.data.led_disp.temp_old)
    scheduler_task                           0x20000010   Data          84  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20000010   Section        0  scheduler.o(.data.scheduler_task)
    HAL_RCC_ADC12_CLK_ENABLED                0x20000070   Data           4  adc.o(.bss.HAL_RCC_ADC12_CLK_ENABLED)
    [Anonymous Symbol]                       0x20000070   Section        0  adc.o(.bss.HAL_RCC_ADC12_CLK_ENABLED)
    TextColor                                0x20000074   Data           2  lcd.o(.bss.TextColor)
    [Anonymous Symbol]                       0x20000074   Section        0  lcd.o(.bss.TextColor)
    STACK                                    0x200007c8   Section     1024  startup_stm32g431xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g431xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g431xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g431xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001d9   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001dd   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001e1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001e1   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001e1   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001e1   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001e9   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001e9   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001ed   Thumb Code     8  startup_stm32g431xx.o(.text)
    ADC1_2_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP1_2_3_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    COMP4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CORDIC_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    CRS_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI0_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI2_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI3_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FLASH_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FMAC_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    FPU_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPTIM1_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    LPUART1_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RCC_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RNG_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SAI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    SPI3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM4_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM7_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_BRK_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    TIM8_UP_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UART4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    UCPD1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART2_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USART3_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USBWakeUp_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_HP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    USB_LP_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    WWDG_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32g431xx.o(.text)
    __aeabi_uldivmod                         0x08000211   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000273   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000273   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000273   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000281   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000281   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000281   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000285   Thumb Code    18  memseta.o(.text)
    __aeabi_uidiv                            0x08000297   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000297   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080002c3   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080002c3   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080002e1   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080002e1   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000301   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000301   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000443   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000449   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800044f   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000533   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000611   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000641   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000671   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000671   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080006a1   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006a1   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080006c5   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006e3   Thumb Code   156  depilogue.o(.text)
    ADC_DMAConvCplt                          0x08000781   Thumb Code    96  stm32g4xx_hal_adc.o(.text.ADC_DMAConvCplt)
    ADC_DMAError                             0x080007e1   Thumb Code    22  stm32g4xx_hal_adc.o(.text.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x080007f9   Thumb Code     6  stm32g4xx_hal_adc.o(.text.ADC_DMAHalfConvCplt)
    ADC_Enable                               0x08000801   Thumb Code   124  stm32g4xx_hal_adc.o(.text.ADC_Enable)
    BusFault_Handler                         0x0800087d   Thumb Code     2  stm32g4xx_it.o(.text.BusFault_Handler)
    DMA1_Channel1_IRQHandler                 0x08000881   Thumb Code    12  stm32g4xx_it.o(.text.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x0800088d   Thumb Code    12  stm32g4xx_it.o(.text.DMA1_Channel2_IRQHandler)
    DMA1_Channel3_IRQHandler                 0x08000899   Thumb Code    12  stm32g4xx_it.o(.text.DMA1_Channel3_IRQHandler)
    DMA1_Channel4_IRQHandler                 0x080008a5   Thumb Code    12  stm32g4xx_it.o(.text.DMA1_Channel4_IRQHandler)
    DebugMon_Handler                         0x080008b1   Thumb Code     2  stm32g4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x080008b5   Thumb Code    10  main.o(.text.Error_Handler)
    HAL_ADCEx_MultiModeConfigChannel         0x080008c1   Thumb Code   194  stm32g4xx_hal_adc_ex.o(.text.HAL_ADCEx_MultiModeConfigChannel)
    HAL_ADC_ConfigChannel                    0x08000985   Thumb Code   820  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08000cb9   Thumb Code     2  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08000cbd   Thumb Code     2  stm32g4xx_hal_adc.o(.text.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08000cc1   Thumb Code     2  stm32g4xx_hal_adc.o(.text.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08000cc5   Thumb Code   512  stm32g4xx_hal_adc.o(.text.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000ec5   Thumb Code   410  adc.o(.text.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08001061   Thumb Code   238  stm32g4xx_hal_adc.o(.text.HAL_ADC_Start_DMA)
    HAL_DMA_Abort                            0x08001151   Thumb Code   110  stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080011c1   Thumb Code   126  stm32g4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08001241   Thumb Code   156  stm32g4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080012dd   Thumb Code   290  stm32g4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001401   Thumb Code   226  stm32g4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x080014e5   Thumb Code    40  stm32g4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x0800150d   Thumb Code   424  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080016b5   Thumb Code    10  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080016c1   Thumb Code    12  stm32g4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080016cd   Thumb Code    12  stm32g4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x080016d9   Thumb Code    26  stm32g4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080016f5   Thumb Code    30  stm32g4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001715   Thumb Code    90  stm32g4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001771   Thumb Code    52  stm32g4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080017a5   Thumb Code    34  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080017c9   Thumb Code    86  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001821   Thumb Code    32  stm32g4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08001841   Thumb Code   242  stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x08001935   Thumb Code    18  stm32g4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x08001949   Thumb Code   692  stm32g4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08001bfd   Thumb Code   786  stm32g4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001f11   Thumb Code    42  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001f3d   Thumb Code    42  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001f69   Thumb Code   130  stm32g4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001fed   Thumb Code  1170  stm32g4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_RTC_GetDate                          0x08002481   Thumb Code   106  stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetDate)
    HAL_RTC_GetTime                          0x080024ed   Thumb Code   118  stm32g4xx_hal_rtc.o(.text.HAL_RTC_GetTime)
    HAL_RTC_Init                             0x08002565   Thumb Code   222  stm32g4xx_hal_rtc.o(.text.HAL_RTC_Init)
    HAL_RTC_MspInit                          0x08002645   Thumb Code    96  rtc.o(.text.HAL_RTC_MspInit)
    HAL_RTC_SetDate                          0x080026a5   Thumb Code   334  stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetDate)
    HAL_RTC_SetTime                          0x080027f5   Thumb Code   390  stm32g4xx_hal_rtc.o(.text.HAL_RTC_SetTime)
    HAL_SYSTICK_Config                       0x0800297d   Thumb Code    44  stm32g4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x080029a9   Thumb Code   198  stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08002a71   Thumb Code   172  stm32g4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002b1d   Thumb Code    98  stm32g4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002b81   Thumb Code   260  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08002c85   Thumb Code   484  stm32g4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_ErrorCallback                    0x08002e69   Thumb Code     2  stm32g4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback)
    HAL_TIM_IC_CaptureCallback               0x08002e6d   Thumb Code     2  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IC_CaptureHalfCpltCallback       0x08002e71   Thumb Code     2  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback)
    HAL_TIM_IC_ConfigChannel                 0x08002e75   Thumb Code   358  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel)
    HAL_TIM_IC_Init                          0x08002fdd   Thumb Code    98  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Init)
    HAL_TIM_IC_MspInit                       0x08003041   Thumb Code     2  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit)
    HAL_TIM_IC_Start_DMA                     0x08003045   Thumb Code   590  stm32g4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA)
    HAL_TIM_PWM_ConfigChannel                0x08003295   Thumb Code   950  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x0800364d   Thumb Code    98  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x080036b1   Thumb Code     2  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_Start                        0x080036b5   Thumb Code   328  stm32g4xx_hal_tim.o(.text.HAL_TIM_PWM_Start)
    HAL_TIM_SlaveConfigSynchro               0x080037fd   Thumb Code    74  stm32g4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro)
    HAL_UARTEx_DisableFifoMode               0x08003849   Thumb Code    54  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003881   Thumb Code    84  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x080038d5   Thumb Code    56  uart_app.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x0800390d   Thumb Code     2  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08003911   Thumb Code   146  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x080039a5   Thumb Code   146  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08003a39   Thumb Code     2  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08003a3d   Thumb Code     2  stm32g4xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x08003a41   Thumb Code     2  stm32g4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003a45   Thumb Code   742  stm32g4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003d2d   Thumb Code    98  stm32g4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003d91   Thumb Code   214  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003e69   Thumb Code     2  stm32g4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003e6d   Thumb Code     2  stm32g4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08003e71   Thumb Code   214  stm32g4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08003f49   Thumb Code     2  stm32g4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003f4d   Thumb Code     2  stm32g4xx_it.o(.text.HardFault_Handler)
    LCD_Clear                                0x08003f51   Thumb Code   290  lcd.o(.text.LCD_Clear)
    LCD_Init                                 0x08004075   Thumb Code   574  lcd.o(.text.LCD_Init)
    LCD_SetBackColor                         0x080042b5   Thumb Code    24  lcd.o(.text.LCD_SetBackColor)
    LCD_SetTextColor                         0x080042cd   Thumb Code    24  lcd.o(.text.LCD_SetTextColor)
    MX_ADC1_Init                             0x080042e5   Thumb Code   176  adc.o(.text.MX_ADC1_Init)
    MX_ADC2_Init                             0x08004395   Thumb Code   146  adc.o(.text.MX_ADC2_Init)
    MX_DMA_Init                              0x08004429   Thumb Code   118  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x080044a1   Thumb Code   332  gpio.o(.text.MX_GPIO_Init)
    MX_RTC_Init                              0x080045ed   Thumb Code   154  rtc.o(.text.MX_RTC_Init)
    MX_TIM1_Init                             0x08004689   Thumb Code   390  tim.o(.text.MX_TIM1_Init)
    MX_TIM2_Init                             0x08004811   Thumb Code   320  tim.o(.text.MX_TIM2_Init)
    MX_TIM3_Init                             0x08004951   Thumb Code   234  tim.o(.text.MX_TIM3_Init)
    MX_USART1_UART_Init                      0x08004a3d   Thumb Code   166  usart.o(.text.MX_USART1_UART_Init)
    MemManage_Handler                        0x08004ae5   Thumb Code     2  stm32g4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08004ae9   Thumb Code     2  stm32g4xx_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x08004aed   Thumb Code     2  stm32g4xx_it.o(.text.PendSV_Handler)
    REG_8230_Init                            0x08004af1   Thumb Code  2674  lcd.o(.text.REG_8230_Init)
    REG_932X_Init                            0x08005565   Thumb Code  5182  lcd.o(.text.REG_932X_Init)
    RTC_ExitInitMode                         0x080069a5   Thumb Code   152  stm32g4xx_hal_rtc.o(.text.RTC_ExitInitMode)
    SVC_Handler                              0x08006a3d   Thumb Code     2  stm32g4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x08006a41   Thumb Code     4  stm32g4xx_it.o(.text.SysTick_Handler)
    SystemInit                               0x08006a45   Thumb Code    18  system_stm32g4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x08006a59   Thumb Code   262  stm32g4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    TIM_DMACaptureCplt                       0x08006b61   Thumb Code   116  stm32g4xx_hal_tim.o(.text.TIM_DMACaptureCplt)
    TIM_DMACaptureHalfCplt                   0x08006bd5   Thumb Code    56  stm32g4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt)
    TIM_DMAError                             0x08006c0d   Thumb Code    92  stm32g4xx_hal_tim.o(.text.TIM_DMAError)
    UART_AdvFeatureConfig                    0x08006d55   Thumb Code   230  stm32g4xx_hal_uart.o(.text.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08006e3d   Thumb Code   198  stm32g4xx_hal_uart.o(.text.UART_CheckIdleState)
    UART_SetConfig                           0x08007005   Thumb Code   644  stm32g4xx_hal_uart.o(.text.UART_SetConfig)
    UART_Start_Receive_DMA                   0x08007299   Thumb Code   130  stm32g4xx_hal_uart.o(.text.UART_Start_Receive_DMA)
    UART_WaitOnFlagUntilTimeout              0x0800731d   Thumb Code   180  stm32g4xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x080073d1   Thumb Code    52  stm32g4xx_it.o(.text.USART1_IRQHandler)
    UsageFault_Handler                       0x08007405   Thumb Code     2  stm32g4xx_it.o(.text.UsageFault_Handler)
    adc_proc                                 0x08007409   Thumb Code   108  adc_app.o(.text.adc_proc)
    fputc                                    0x0800747d   Thumb Code    30  usart.o(.text.fputc)
    ic_proc                                  0x0800749d   Thumb Code    92  tim_app.o(.text.ic_proc)
    key_init                                 0x080074fd   Thumb Code    64  key_app.o(.text.key_init)
    key_proc                                 0x0800753d   Thumb Code   124  key_app.o(.text.key_proc)
    lcd_proc                                 0x080075b9   Thumb Code     2  lcd_app.o(.text.lcd_proc)
    led_proc                                 0x080075bd   Thumb Code   102  led_app.o(.text.led_proc)
    limit_value                              0x08007625   Thumb Code    52  filter.o(.text.limit_value)
    main                                     0x08007659   Thumb Code   306  main.o(.text.main)
    ringbuffer_init                          0x0800778d   Thumb Code    14  ringbuffer.o(.text.ringbuffer_init)
    ringbuffer_is_empty                      0x0800779d   Thumb Code    10  ringbuffer.o(.text.ringbuffer_is_empty)
    ringbuffer_is_full                       0x080077a9   Thumb Code    12  ringbuffer.o(.text.ringbuffer_is_full)
    ringbuffer_read                          0x080077b5   Thumb Code    92  ringbuffer.o(.text.ringbuffer_read)
    ringbuffer_write                         0x08007811   Thumb Code    94  ringbuffer.o(.text.ringbuffer_write)
    rtc_proc                                 0x08007871   Thumb Code    46  rtc_app.o(.text.rtc_proc)
    scheduler_init                           0x080078a1   Thumb Code    14  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x080078b1   Thumb Code    74  scheduler.o(.text.scheduler_run)
    system_init                              0x080078fd   Thumb Code    48  system.o(.text.system_init)
    uart_proc                                0x0800792d   Thumb Code    60  uart_app.o(.text.uart_proc)
    __0printf                                0x08007981   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08007981   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08007981   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08007981   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08007981   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x080079a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080079af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080079b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    AHBPrescTable                            0x08008272   Data          16  system_stm32g4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08008282   Data           8  system_stm32g4xx.o(.rodata.APBPrescTable)
    UARTPrescTable                           0x0800828a   Data          24  stm32g4xx_hal_uart.o(.rodata.UARTPrescTable)
    Region$$Table$$Base                      0x080082c4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080082e4   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000000   Data           4  stdout.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32g4xx.o(.data.SystemCoreClock)
    uwTickFreq                               0x20000064   Data           4  stm32g4xx_hal.o(.data.uwTickFreq)
    uwTickPrio                               0x20000068   Data           4  stm32g4xx_hal.o(.data.uwTickPrio)
    adc_value                                0x20000078   Data           8  adc_app.o(.bss.adc_value)
    btns                                     0x20000080   Data          64  key_app.o(.bss.btns)
    date                                     0x200000c0   Data           4  rtc_app.o(.bss.date)
    dma_buff                                 0x200000c4   Data         240  adc_app.o(.bss.dma_buff)
    dummy                                    0x200001b4   Data           2  lcd.o(.bss.dummy)
    hadc1                                    0x200001b8   Data         108  adc.o(.bss.hadc1)
    hadc2                                    0x20000224   Data         108  adc.o(.bss.hadc2)
    hdma_adc1                                0x20000290   Data          96  adc.o(.bss.hdma_adc1)
    hdma_adc2                                0x200002f0   Data          96  adc.o(.bss.hdma_adc2)
    hdma_tim3_ch1                            0x20000350   Data          96  tim.o(.bss.hdma_tim3_ch1)
    hdma_usart1_rx                           0x200003b0   Data          96  usart.o(.bss.hdma_usart1_rx)
    hrtc                                     0x20000410   Data          40  rtc.o(.bss.hrtc)
    htim1                                    0x20000438   Data          76  tim.o(.bss.htim1)
    htim2                                    0x20000484   Data          76  tim.o(.bss.htim2)
    htim3                                    0x200004d0   Data          76  tim.o(.bss.htim3)
    huart1                                   0x2000051c   Data         144  usart.o(.bss.huart1)
    key_down                                 0x200005ac   Data           1  key_app.o(.bss.key_down)
    key_old                                  0x200005ad   Data           1  key_app.o(.bss.key_old)
    key_up                                   0x200005ae   Data           1  key_app.o(.bss.key_up)
    key_val                                  0x200005af   Data           1  key_app.o(.bss.key_val)
    task_num                                 0x200005b0   Data           1  scheduler.o(.bss.task_num)
    tim_ic_buffer                            0x200005b4   Data         256  tim_app.o(.bss.tim_ic_buffer)
    tim_ic_temp                              0x200006b4   Data           4  tim_app.o(.bss.tim_ic_temp)
    tim_ic_val                               0x200006b8   Data           4  tim_app.o(.bss.tim_ic_val)
    time                                     0x200006bc   Data          20  rtc_app.o(.bss.time)
    uart_rx_dma_buffer                       0x200006d0   Data         128  usart.o(.bss.uart_rx_dma_buffer)
    ucLed                                    0x20000750   Data           8  led_app.o(.bss.ucLed)
    usart_rb                                 0x20000758   Data          44  uart_app.o(.bss.usart_rb)
    usart_read_buffer                        0x20000784   Data          64  uart_app.o(.bss.usart_read_buffer)
    uwTick                                   0x200007c4   Data           4  stm32g4xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20000bc8   Data           0  startup_stm32g431xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008358, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000082e4, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g431xx.o
    0x080001d8   0x080001d8   0x00000000   Code   RO         1809  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001d8   0x080001d8   0x00000004   Code   RO         2111    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001dc   0x080001dc   0x00000004   Code   RO         2114    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         2116    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         2118    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001e0   0x080001e0   0x00000008   Code   RO         2119    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         2121    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001e8   0x080001e8   0x00000000   Code   RO         2123    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001e8   0x080001e8   0x00000004   Code   RO         2112    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001ec   0x080001ec   0x00000024   Code   RO            4    .text               startup_stm32g431xx.o
    0x08000210   0x08000210   0x00000062   Code   RO         1812    .text               mc_w.l(uldiv.o)
    0x08000272   0x08000272   0x00000024   Code   RO         1818    .text               mc_w.l(memseta.o)
    0x08000296   0x08000296   0x0000002c   Code   RO         2135    .text               mc_w.l(uidiv.o)
    0x080002c2   0x080002c2   0x0000001e   Code   RO         2137    .text               mc_w.l(llshl.o)
    0x080002e0   0x080002e0   0x00000020   Code   RO         2139    .text               mc_w.l(llushr.o)
    0x08000300   0x08000300   0x00000000   Code   RO         2143    .text               mc_w.l(iusefp.o)
    0x08000300   0x08000300   0x0000014e   Code   RO         2144    .text               mf_w.l(dadd.o)
    0x0800044e   0x0800044e   0x000000e4   Code   RO         2146    .text               mf_w.l(dmul.o)
    0x08000532   0x08000532   0x000000de   Code   RO         2148    .text               mf_w.l(ddiv.o)
    0x08000610   0x08000610   0x00000030   Code   RO         2150    .text               mf_w.l(dfixul.o)
    0x08000640   0x08000640   0x00000030   Code   RO         2152    .text               mf_w.l(cdrcmple.o)
    0x08000670   0x08000670   0x00000030   Code   RO         2154    .text               mc_w.l(init.o)
    0x080006a0   0x080006a0   0x00000024   Code   RO         2156    .text               mc_w.l(llsshr.o)
    0x080006c4   0x080006c4   0x000000ba   Code   RO         2158    .text               mf_w.l(depilogue.o)
    0x0800077e   0x0800077e   0x00000002   PAD
    0x08000780   0x08000780   0x00000060   Code   RO          197    .text.ADC_DMAConvCplt  stm32g4xx_hal_adc.o
    0x080007e0   0x080007e0   0x00000016   Code   RO          201    .text.ADC_DMAError  stm32g4xx_hal_adc.o
    0x080007f6   0x080007f6   0x00000002   PAD
    0x080007f8   0x080007f8   0x00000006   Code   RO          199    .text.ADC_DMAHalfConvCplt  stm32g4xx_hal_adc.o
    0x080007fe   0x080007fe   0x00000002   PAD
    0x08000800   0x08000800   0x0000007c   Code   RO          183    .text.ADC_Enable    stm32g4xx_hal_adc.o
    0x0800087c   0x0800087c   0x00000002   Code   RO          132    .text.BusFault_Handler  stm32g4xx_it.o
    0x0800087e   0x0800087e   0x00000002   PAD
    0x08000880   0x08000880   0x0000000c   Code   RO          144    .text.DMA1_Channel1_IRQHandler  stm32g4xx_it.o
    0x0800088c   0x0800088c   0x0000000c   Code   RO          146    .text.DMA1_Channel2_IRQHandler  stm32g4xx_it.o
    0x08000898   0x08000898   0x0000000c   Code   RO          148    .text.DMA1_Channel3_IRQHandler  stm32g4xx_it.o
    0x080008a4   0x080008a4   0x0000000c   Code   RO          150    .text.DMA1_Channel4_IRQHandler  stm32g4xx_it.o
    0x080008b0   0x080008b0   0x00000002   Code   RO          138    .text.DebugMon_Handler  stm32g4xx_it.o
    0x080008b2   0x080008b2   0x00000002   PAD
    0x080008b4   0x080008b4   0x0000000a   Code   RO           15    .text.Error_Handler  main.o
    0x080008be   0x080008be   0x00000002   PAD
    0x080008c0   0x080008c0   0x000000c2   Code   RO          281    .text.HAL_ADCEx_MultiModeConfigChannel  stm32g4xx_hal_adc_ex.o
    0x08000982   0x08000982   0x00000002   PAD
    0x08000984   0x08000984   0x00000334   Code   RO          221    .text.HAL_ADC_ConfigChannel  stm32g4xx_hal_adc.o
    0x08000cb8   0x08000cb8   0x00000002   Code   RO          213    .text.HAL_ADC_ConvCpltCallback  stm32g4xx_hal_adc.o
    0x08000cba   0x08000cba   0x00000002   PAD
    0x08000cbc   0x08000cbc   0x00000002   Code   RO          219    .text.HAL_ADC_ConvHalfCpltCallback  stm32g4xx_hal_adc.o
    0x08000cbe   0x08000cbe   0x00000002   PAD
    0x08000cc0   0x08000cc0   0x00000002   Code   RO          217    .text.HAL_ADC_ErrorCallback  stm32g4xx_hal_adc.o
    0x08000cc2   0x08000cc2   0x00000002   PAD
    0x08000cc4   0x08000cc4   0x00000200   Code   RO          169    .text.HAL_ADC_Init  stm32g4xx_hal_adc.o
    0x08000ec4   0x08000ec4   0x0000019a   Code   RO           39    .text.HAL_ADC_MspInit  adc.o
    0x0800105e   0x0800105e   0x00000002   PAD
    0x08001060   0x08001060   0x000000ee   Code   RO          195    .text.HAL_ADC_Start_DMA  stm32g4xx_hal_adc.o
    0x0800114e   0x0800114e   0x00000002   PAD
    0x08001150   0x08001150   0x0000006e   Code   RO          593    .text.HAL_DMA_Abort  stm32g4xx_hal_dma.o
    0x080011be   0x080011be   0x00000002   PAD
    0x080011c0   0x080011c0   0x0000007e   Code   RO          595    .text.HAL_DMA_Abort_IT  stm32g4xx_hal_dma.o
    0x0800123e   0x0800123e   0x00000002   PAD
    0x08001240   0x08001240   0x0000009c   Code   RO          599    .text.HAL_DMA_IRQHandler  stm32g4xx_hal_dma.o
    0x080012dc   0x080012dc   0x00000122   Code   RO          585    .text.HAL_DMA_Init  stm32g4xx_hal_dma.o
    0x080013fe   0x080013fe   0x00000002   PAD
    0x08001400   0x08001400   0x000000e2   Code   RO          591    .text.HAL_DMA_Start_IT  stm32g4xx_hal_dma.o
    0x080014e2   0x080014e2   0x00000002   PAD
    0x080014e4   0x080014e4   0x00000028   Code   RO          320    .text.HAL_Delay     stm32g4xx_hal.o
    0x0800150c   0x0800150c   0x000001a8   Code   RO          535    .text.HAL_GPIO_Init  stm32g4xx_hal_gpio.o
    0x080016b4   0x080016b4   0x0000000a   Code   RO          539    .text.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x080016be   0x080016be   0x00000002   PAD
    0x080016c0   0x080016c0   0x0000000c   Code   RO          541    .text.HAL_GPIO_WritePin  stm32g4xx_hal_gpio.o
    0x080016cc   0x080016cc   0x0000000c   Code   RO          312    .text.HAL_GetTick   stm32g4xx_hal.o
    0x080016d8   0x080016d8   0x0000001a   Code   RO          310    .text.HAL_IncTick   stm32g4xx_hal.o
    0x080016f2   0x080016f2   0x00000002   PAD
    0x080016f4   0x080016f4   0x0000001e   Code   RO          300    .text.HAL_Init      stm32g4xx_hal.o
    0x08001712   0x08001712   0x00000002   PAD
    0x08001714   0x08001714   0x0000005a   Code   RO          302    .text.HAL_InitTick  stm32g4xx_hal.o
    0x0800176e   0x0800176e   0x00000002   PAD
    0x08001770   0x08001770   0x00000034   Code   RO          161    .text.HAL_MspInit   stm32g4xx_hal_msp.o
    0x080017a4   0x080017a4   0x00000022   Code   RO          761    .text.HAL_NVIC_EnableIRQ  stm32g4xx_hal_cortex.o
    0x080017c6   0x080017c6   0x00000002   PAD
    0x080017c8   0x080017c8   0x00000056   Code   RO          759    .text.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x0800181e   0x0800181e   0x00000002   PAD
    0x08001820   0x08001820   0x00000020   Code   RO          757    .text.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x08001840   0x08001840   0x000000f2   Code   RO          677    .text.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x08001932   0x08001932   0x00000002   PAD
    0x08001934   0x08001934   0x00000012   Code   RO          747    .text.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x08001946   0x08001946   0x00000002   PAD
    0x08001948   0x08001948   0x000002b4   Code   RO          419    .text.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x08001bfc   0x08001bfc   0x00000312   Code   RO          385    .text.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x08001f0e   0x08001f0e   0x00000002   PAD
    0x08001f10   0x08001f10   0x0000002a   Code   RO          393    .text.HAL_RCC_GetPCLK1Freq  stm32g4xx_hal_rcc.o
    0x08001f3a   0x08001f3a   0x00000002   PAD
    0x08001f3c   0x08001f3c   0x0000002a   Code   RO          395    .text.HAL_RCC_GetPCLK2Freq  stm32g4xx_hal_rcc.o
    0x08001f66   0x08001f66   0x00000002   PAD
    0x08001f68   0x08001f68   0x00000082   Code   RO          387    .text.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x08001fea   0x08001fea   0x00000002   PAD
    0x08001fec   0x08001fec   0x00000492   Code   RO          383    .text.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x0800247e   0x0800247e   0x00000002   PAD
    0x08002480   0x08002480   0x0000006a   Code   RO          827    .text.HAL_RTC_GetDate  stm32g4xx_hal_rtc.o
    0x080024ea   0x080024ea   0x00000002   PAD
    0x080024ec   0x080024ec   0x00000076   Code   RO          821    .text.HAL_RTC_GetTime  stm32g4xx_hal_rtc.o
    0x08002562   0x08002562   0x00000002   PAD
    0x08002564   0x08002564   0x000000de   Code   RO          803    .text.HAL_RTC_Init  stm32g4xx_hal_rtc.o
    0x08002642   0x08002642   0x00000002   PAD
    0x08002644   0x08002644   0x00000060   Code   RO           67    .text.HAL_RTC_MspInit  rtc.o
    0x080026a4   0x080026a4   0x0000014e   Code   RO          825    .text.HAL_RTC_SetDate  stm32g4xx_hal_rtc.o
    0x080027f2   0x080027f2   0x00000002   PAD
    0x080027f4   0x080027f4   0x00000186   Code   RO          817    .text.HAL_RTC_SetTime  stm32g4xx_hal_rtc.o
    0x0800297a   0x0800297a   0x00000002   PAD
    0x0800297c   0x0800297c   0x0000002c   Code   RO          769    .text.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x080029a8   0x080029a8   0x000000c6   Code   RO         1256    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32g4xx_hal_tim_ex.o
    0x08002a6e   0x08002a6e   0x00000002   PAD
    0x08002a70   0x08002a70   0x000000ac   Code   RO         1254    .text.HAL_TIMEx_MasterConfigSynchronization  stm32g4xx_hal_tim_ex.o
    0x08002b1c   0x08002b1c   0x00000062   Code   RO          959    .text.HAL_TIM_Base_Init  stm32g4xx_hal_tim.o
    0x08002b7e   0x08002b7e   0x00000002   PAD
    0x08002b80   0x08002b80   0x00000104   Code   RO           88    .text.HAL_TIM_Base_MspInit  tim.o
    0x08002c84   0x08002c84   0x000001e4   Code   RO         1139    .text.HAL_TIM_ConfigClockSource  stm32g4xx_hal_tim.o
    0x08002e68   0x08002e68   0x00000002   Code   RO         1159    .text.HAL_TIM_ErrorCallback  stm32g4xx_hal_tim.o
    0x08002e6a   0x08002e6a   0x00000002   PAD
    0x08002e6c   0x08002e6c   0x00000002   Code   RO         1095    .text.HAL_TIM_IC_CaptureCallback  stm32g4xx_hal_tim.o
    0x08002e6e   0x08002e6e   0x00000002   PAD
    0x08002e70   0x08002e70   0x00000002   Code   RO         1153    .text.HAL_TIM_IC_CaptureHalfCpltCallback  stm32g4xx_hal_tim.o
    0x08002e72   0x08002e72   0x00000002   PAD
    0x08002e74   0x08002e74   0x00000166   Code   RO         1109    .text.HAL_TIM_IC_ConfigChannel  stm32g4xx_hal_tim.o
    0x08002fda   0x08002fda   0x00000002   PAD
    0x08002fdc   0x08002fdc   0x00000062   Code   RO         1033    .text.HAL_TIM_IC_Init  stm32g4xx_hal_tim.o
    0x0800303e   0x0800303e   0x00000002   PAD
    0x08003040   0x08003040   0x00000002   Code   RO         1035    .text.HAL_TIM_IC_MspInit  stm32g4xx_hal_tim.o
    0x08003042   0x08003042   0x00000002   PAD
    0x08003044   0x08003044   0x0000024e   Code   RO         1049    .text.HAL_TIM_IC_Start_DMA  stm32g4xx_hal_tim.o
    0x08003292   0x08003292   0x00000002   PAD
    0x08003294   0x08003294   0x000003b6   Code   RO         1113    .text.HAL_TIM_PWM_ConfigChannel  stm32g4xx_hal_tim.o
    0x0800364a   0x0800364a   0x00000002   PAD
    0x0800364c   0x0800364c   0x00000062   Code   RO         1013    .text.HAL_TIM_PWM_Init  stm32g4xx_hal_tim.o
    0x080036ae   0x080036ae   0x00000002   PAD
    0x080036b0   0x080036b0   0x00000002   Code   RO         1015    .text.HAL_TIM_PWM_MspInit  stm32g4xx_hal_tim.o
    0x080036b2   0x080036b2   0x00000002   PAD
    0x080036b4   0x080036b4   0x00000148   Code   RO         1021    .text.HAL_TIM_PWM_Start  stm32g4xx_hal_tim.o
    0x080037fc   0x080037fc   0x0000004a   Code   RO         1143    .text.HAL_TIM_SlaveConfigSynchro  stm32g4xx_hal_tim.o
    0x08003846   0x08003846   0x00000002   PAD
    0x08003848   0x08003848   0x00000036   Code   RO         1495    .text.HAL_UARTEx_DisableFifoMode  stm32g4xx_hal_uart_ex.o
    0x0800387e   0x0800387e   0x00000002   PAD
    0x08003880   0x08003880   0x00000054   Code   RO         1505    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32g4xx_hal_uart_ex.o
    0x080038d4   0x080038d4   0x00000038   Code   RO         1680    .text.HAL_UARTEx_RxEventCallback  uart_app.o
    0x0800390c   0x0800390c   0x00000002   Code   RO         1481    .text.HAL_UARTEx_RxFifoFullCallback  stm32g4xx_hal_uart_ex.o
    0x0800390e   0x0800390e   0x00000002   PAD
    0x08003910   0x08003910   0x00000092   Code   RO         1499    .text.HAL_UARTEx_SetRxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x080039a2   0x080039a2   0x00000002   PAD
    0x080039a4   0x080039a4   0x00000092   Code   RO         1497    .text.HAL_UARTEx_SetTxFifoThreshold  stm32g4xx_hal_uart_ex.o
    0x08003a36   0x08003a36   0x00000002   PAD
    0x08003a38   0x08003a38   0x00000002   Code   RO         1483    .text.HAL_UARTEx_TxFifoEmptyCallback  stm32g4xx_hal_uart_ex.o
    0x08003a3a   0x08003a3a   0x00000002   PAD
    0x08003a3c   0x08003a3c   0x00000002   Code   RO         1479    .text.HAL_UARTEx_WakeupCallback  stm32g4xx_hal_uart_ex.o
    0x08003a3e   0x08003a3e   0x00000002   PAD
    0x08003a40   0x08003a40   0x00000002   Code   RO         1421    .text.HAL_UART_ErrorCallback  stm32g4xx_hal_uart.o
    0x08003a42   0x08003a42   0x00000002   PAD
    0x08003a44   0x08003a44   0x000002e6   Code   RO         1417    .text.HAL_UART_IRQHandler  stm32g4xx_hal_uart.o
    0x08003d2a   0x08003d2a   0x00000002   PAD
    0x08003d2c   0x08003d2c   0x00000062   Code   RO         1333    .text.HAL_UART_Init  stm32g4xx_hal_uart.o
    0x08003d8e   0x08003d8e   0x00000002   PAD
    0x08003d90   0x08003d90   0x000000d6   Code   RO          106    .text.HAL_UART_MspInit  usart.o
    0x08003e66   0x08003e66   0x00000002   PAD
    0x08003e68   0x08003e68   0x00000002   Code   RO         1429    .text.HAL_UART_RxCpltCallback  stm32g4xx_hal_uart.o
    0x08003e6a   0x08003e6a   0x00000002   PAD
    0x08003e6c   0x08003e6c   0x00000002   Code   RO         1431    .text.HAL_UART_RxHalfCpltCallback  stm32g4xx_hal_uart.o
    0x08003e6e   0x08003e6e   0x00000002   PAD
    0x08003e70   0x08003e70   0x000000d6   Code   RO         1353    .text.HAL_UART_Transmit  stm32g4xx_hal_uart.o
    0x08003f46   0x08003f46   0x00000002   PAD
    0x08003f48   0x08003f48   0x00000002   Code   RO         1425    .text.HAL_UART_TxCpltCallback  stm32g4xx_hal_uart.o
    0x08003f4a   0x08003f4a   0x00000002   PAD
    0x08003f4c   0x08003f4c   0x00000002   Code   RO          128    .text.HardFault_Handler  stm32g4xx_it.o
    0x08003f4e   0x08003f4e   0x00000002   PAD
    0x08003f50   0x08003f50   0x00000122   Code   RO         1590    .text.LCD_Clear     lcd.o
    0x08004072   0x08004072   0x00000002   PAD
    0x08004074   0x08004074   0x0000023e   Code   RO         1576    .text.LCD_Init      lcd.o
    0x080042b2   0x080042b2   0x00000002   PAD
    0x080042b4   0x080042b4   0x00000018   Code   RO         1584    .text.LCD_SetBackColor  lcd.o
    0x080042cc   0x080042cc   0x00000018   Code   RO         1582    .text.LCD_SetTextColor  lcd.o
    0x080042e4   0x080042e4   0x000000b0   Code   RO           35    .text.MX_ADC1_Init  adc.o
    0x08004394   0x08004394   0x00000092   Code   RO           37    .text.MX_ADC2_Init  adc.o
    0x08004426   0x08004426   0x00000002   PAD
    0x08004428   0x08004428   0x00000076   Code   RO           56    .text.MX_DMA_Init   dma.o
    0x0800449e   0x0800449e   0x00000002   PAD
    0x080044a0   0x080044a0   0x0000014c   Code   RO           26    .text.MX_GPIO_Init  gpio.o
    0x080045ec   0x080045ec   0x0000009a   Code   RO           65    .text.MX_RTC_Init   rtc.o
    0x08004686   0x08004686   0x00000002   PAD
    0x08004688   0x08004688   0x00000186   Code   RO           80    .text.MX_TIM1_Init  tim.o
    0x0800480e   0x0800480e   0x00000002   PAD
    0x08004810   0x08004810   0x00000140   Code   RO           84    .text.MX_TIM2_Init  tim.o
    0x08004950   0x08004950   0x000000ea   Code   RO           86    .text.MX_TIM3_Init  tim.o
    0x08004a3a   0x08004a3a   0x00000002   PAD
    0x08004a3c   0x08004a3c   0x000000a6   Code   RO          104    .text.MX_USART1_UART_Init  usart.o
    0x08004ae2   0x08004ae2   0x00000002   PAD
    0x08004ae4   0x08004ae4   0x00000002   Code   RO          130    .text.MemManage_Handler  stm32g4xx_it.o
    0x08004ae6   0x08004ae6   0x00000002   PAD
    0x08004ae8   0x08004ae8   0x00000002   Code   RO          126    .text.NMI_Handler   stm32g4xx_it.o
    0x08004aea   0x08004aea   0x00000002   PAD
    0x08004aec   0x08004aec   0x00000002   Code   RO          140    .text.PendSV_Handler  stm32g4xx_it.o
    0x08004aee   0x08004aee   0x00000002   PAD
    0x08004af0   0x08004af0   0x00000a72   Code   RO         1570    .text.REG_8230_Init  lcd.o
    0x08005562   0x08005562   0x00000002   PAD
    0x08005564   0x08005564   0x0000143e   Code   RO         1574    .text.REG_932X_Init  lcd.o
    0x080069a2   0x080069a2   0x00000002   PAD
    0x080069a4   0x080069a4   0x00000098   Code   RO          809    .text.RTC_ExitInitMode  stm32g4xx_hal_rtc.o
    0x08006a3c   0x08006a3c   0x00000002   Code   RO          136    .text.SVC_Handler   stm32g4xx_it.o
    0x08006a3e   0x08006a3e   0x00000002   PAD
    0x08006a40   0x08006a40   0x00000004   Code   RO          142    .text.SysTick_Handler  stm32g4xx_it.o
    0x08006a44   0x08006a44   0x00000012   Code   RO         1516    .text.SystemInit    system_stm32g4xx.o
    0x08006a56   0x08006a56   0x00000002   PAD
    0x08006a58   0x08006a58   0x00000106   Code   RO          963    .text.TIM_Base_SetConfig  stm32g4xx_hal_tim.o
    0x08006b5e   0x08006b5e   0x00000002   PAD
    0x08006b60   0x08006b60   0x00000074   Code   RO         1051    .text.TIM_DMACaptureCplt  stm32g4xx_hal_tim.o
    0x08006bd4   0x08006bd4   0x00000038   Code   RO         1053    .text.TIM_DMACaptureHalfCplt  stm32g4xx_hal_tim.o
    0x08006c0c   0x08006c0c   0x0000005c   Code   RO          983    .text.TIM_DMAError  stm32g4xx_hal_tim.o
    0x08006c68   0x08006c68   0x000000ea   Code   RO         1145    .text.TIM_SlaveTimer_SetConfig  stm32g4xx_hal_tim.o
    0x08006d52   0x08006d52   0x00000002   PAD
    0x08006d54   0x08006d54   0x000000e6   Code   RO         1339    .text.UART_AdvFeatureConfig  stm32g4xx_hal_uart.o
    0x08006e3a   0x08006e3a   0x00000002   PAD
    0x08006e3c   0x08006e3c   0x000000c6   Code   RO         1341    .text.UART_CheckIdleState  stm32g4xx_hal_uart.o
    0x08006f02   0x08006f02   0x00000002   PAD
    0x08006f04   0x08006f04   0x00000010   Code   RO         1419    .text.UART_DMAAbortOnError  stm32g4xx_hal_uart.o
    0x08006f14   0x08006f14   0x00000088   Code   RO         1379    .text.UART_DMAError  stm32g4xx_hal_uart.o
    0x08006f9c   0x08006f9c   0x00000050   Code   RO         1463    .text.UART_DMAReceiveCplt  stm32g4xx_hal_uart.o
    0x08006fec   0x08006fec   0x00000016   Code   RO         1465    .text.UART_DMARxHalfCplt  stm32g4xx_hal_uart.o
    0x08007002   0x08007002   0x00000002   PAD
    0x08007004   0x08007004   0x00000294   Code   RO         1337    .text.UART_SetConfig  stm32g4xx_hal_uart.o
    0x08007298   0x08007298   0x00000082   Code   RO         1383    .text.UART_Start_Receive_DMA  stm32g4xx_hal_uart.o
    0x0800731a   0x0800731a   0x00000002   PAD
    0x0800731c   0x0800731c   0x000000b4   Code   RO         1355    .text.UART_WaitOnFlagUntilTimeout  stm32g4xx_hal_uart.o
    0x080073d0   0x080073d0   0x00000034   Code   RO          152    .text.USART1_IRQHandler  stm32g4xx_it.o
    0x08007404   0x08007404   0x00000002   Code   RO          134    .text.UsageFault_Handler  stm32g4xx_it.o
    0x08007406   0x08007406   0x00000002   PAD
    0x08007408   0x08007408   0x00000074   Code   RO         1712    .text.adc_proc      adc_app.o
    0x0800747c   0x0800747c   0x0000001e   Code   RO          110    .text.fputc         usart.o
    0x0800749a   0x0800749a   0x00000002   PAD
    0x0800749c   0x0800749c   0x00000060   Code   RO         1797    .text.ic_proc       tim_app.o
    0x080074fc   0x080074fc   0x00000040   Code   RO         1549    .text.key_init      key_app.o
    0x0800753c   0x0800753c   0x0000007c   Code   RO         1547    .text.key_proc      key_app.o
    0x080075b8   0x080075b8   0x00000002   Code   RO         1648    .text.lcd_proc      lcd_app.o
    0x080075ba   0x080075ba   0x00000002   PAD
    0x080075bc   0x080075bc   0x00000066   Code   RO         1660    .text.led_proc      led_app.o
    0x08007622   0x08007622   0x00000002   PAD
    0x08007624   0x08007624   0x00000034   Code   RO         1722    .text.limit_value   filter.o
    0x08007658   0x08007658   0x00000132   Code   RO           11    .text.main          main.o
    0x0800778a   0x0800778a   0x00000002   PAD
    0x0800778c   0x0800778c   0x0000000e   Code   RO         1694    .text.ringbuffer_init  ringbuffer.o
    0x0800779a   0x0800779a   0x00000002   PAD
    0x0800779c   0x0800779c   0x0000000a   Code   RO         1698    .text.ringbuffer_is_empty  ringbuffer.o
    0x080077a6   0x080077a6   0x00000002   PAD
    0x080077a8   0x080077a8   0x0000000c   Code   RO         1696    .text.ringbuffer_is_full  ringbuffer.o
    0x080077b4   0x080077b4   0x0000005c   Code   RO         1702    .text.ringbuffer_read  ringbuffer.o
    0x08007810   0x08007810   0x0000005e   Code   RO         1700    .text.ringbuffer_write  ringbuffer.o
    0x0800786e   0x0800786e   0x00000002   PAD
    0x08007870   0x08007870   0x0000002e   Code   RO         1783    .text.rtc_proc      rtc_app.o
    0x0800789e   0x0800789e   0x00000002   PAD
    0x080078a0   0x080078a0   0x0000000e   Code   RO         1531    .text.scheduler_init  scheduler.o
    0x080078ae   0x080078ae   0x00000002   PAD
    0x080078b0   0x080078b0   0x0000004a   Code   RO         1533    .text.scheduler_run  scheduler.o
    0x080078fa   0x080078fa   0x00000002   PAD
    0x080078fc   0x080078fc   0x00000030   Code   RO         1672    .text.system_init   system.o
    0x0800792c   0x0800792c   0x00000054   Code   RO         1682    .text.uart_proc     uart_app.o
    0x08007980   0x08007980   0x00000020   Code   RO         2055    i.__0printf         mc_w.l(printfa.o)
    0x080079a0   0x080079a0   0x0000000e   Code   RO         2162    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080079ae   0x080079ae   0x00000002   Code   RO         2163    i.__scatterload_null  mc_w.l(handlers.o)
    0x080079b0   0x080079b0   0x0000000e   Code   RO         2164    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080079be   0x080079be   0x00000002   PAD
    0x080079c0   0x080079c0   0x00000184   Code   RO         2062    i._fp_digits        mc_w.l(printfa.o)
    0x08007b44   0x08007b44   0x000006dc   Code   RO         2063    i._printf_core      mc_w.l(printfa.o)
    0x08008220   0x08008220   0x00000024   Code   RO         2064    i._printf_post_padding  mc_w.l(printfa.o)
    0x08008244   0x08008244   0x0000002e   Code   RO         2065    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08008272   0x08008272   0x00000010   Data   RO         1521    .rodata.AHBPrescTable  system_stm32g4xx.o
    0x08008282   0x08008282   0x00000008   Data   RO         1522    .rodata.APBPrescTable  system_stm32g4xx.o
    0x0800828a   0x0800828a   0x00000018   Data   RO         1467    .rodata.UARTPrescTable  stm32g4xx_hal_uart.o
    0x080082a2   0x080082a2   0x00000002   PAD
    0x080082a4   0x080082a4   0x00000010   Data   RO         1468    .rodata.cst16       stm32g4xx_hal_uart.o
    0x080082b4   0x080082b4   0x00000010   Data   RO         1507    .rodata.cst8        stm32g4xx_hal_uart_ex.o
    0x080082c4   0x080082c4   0x00000020   Data   RO         2161    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080082e8, Size: 0x00000bc8, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080082e8   0x00000004   Data   RW         2125    .data               mc_w.l(stdout.o)
    0x20000004   0x080082ec   0x00000002   Data   RW         1637    .data.BackColor     lcd.o
    0x20000006   0x080082ee   0x00000002   PAD
    0x20000008   0x080082f0   0x00000004   Data   RW         1520    .data.SystemCoreClock  system_stm32g4xx.o
    0x2000000c   0x080082f4   0x00000001   Data   RW         1662    .data.led_disp.temp_old  led_app.o
    0x2000000d   0x080082f5   0x00000003   PAD
    0x20000010   0x080082f8   0x00000054   Data   RW         1536    .data.scheduler_task  scheduler.o
    0x20000064   0x0800834c   0x00000004   Data   RW          371    .data.uwTickFreq    stm32g4xx_hal.o
    0x20000068   0x08008350   0x00000004   Data   RW          370    .data.uwTickPrio    stm32g4xx_hal.o
    0x2000006c   0x08008354   0x00000004   PAD
    0x20000070        -       0x00000004   Zero   RW           45    .bss.HAL_RCC_ADC12_CLK_ENABLED  adc.o
    0x20000074        -       0x00000002   Zero   RW         1636    .bss.TextColor      lcd.o
    0x20000076   0x08008354   0x00000002   PAD
    0x20000078        -       0x00000008   Zero   RW         1715    .bss.adc_value      adc_app.o
    0x20000080        -       0x00000040   Zero   RW         1559    .bss.btns           key_app.o
    0x200000c0        -       0x00000004   Zero   RW         1786    .bss.date           rtc_app.o
    0x200000c4        -       0x000000f0   Zero   RW         1714    .bss.dma_buff       adc_app.o
    0x200001b4        -       0x00000002   Zero   RW         1635    .bss.dummy          lcd.o
    0x200001b6   0x08008354   0x00000002   PAD
    0x200001b8        -       0x0000006c   Zero   RW           43    .bss.hadc1          adc.o
    0x20000224        -       0x0000006c   Zero   RW           44    .bss.hadc2          adc.o
    0x20000290        -       0x00000060   Zero   RW           46    .bss.hdma_adc1      adc.o
    0x200002f0        -       0x00000060   Zero   RW           47    .bss.hdma_adc2      adc.o
    0x20000350        -       0x00000060   Zero   RW           95    .bss.hdma_tim3_ch1  tim.o
    0x200003b0        -       0x00000060   Zero   RW          117    .bss.hdma_usart1_rx  usart.o
    0x20000410        -       0x00000028   Zero   RW           71    .bss.hrtc           rtc.o
    0x20000438        -       0x0000004c   Zero   RW           92    .bss.htim1          tim.o
    0x20000484        -       0x0000004c   Zero   RW           93    .bss.htim2          tim.o
    0x200004d0        -       0x0000004c   Zero   RW           94    .bss.htim3          tim.o
    0x2000051c        -       0x00000090   Zero   RW          116    .bss.huart1         usart.o
    0x200005ac        -       0x00000001   Zero   RW         1557    .bss.key_down       key_app.o
    0x200005ad        -       0x00000001   Zero   RW         1556    .bss.key_old        key_app.o
    0x200005ae        -       0x00000001   Zero   RW         1558    .bss.key_up         key_app.o
    0x200005af        -       0x00000001   Zero   RW         1555    .bss.key_val        key_app.o
    0x200005b0        -       0x00000001   Zero   RW         1535    .bss.task_num       scheduler.o
    0x200005b1   0x08008354   0x00000003   PAD
    0x200005b4        -       0x00000100   Zero   RW         1801    .bss.tim_ic_buffer  tim_app.o
    0x200006b4        -       0x00000004   Zero   RW         1800    .bss.tim_ic_temp    tim_app.o
    0x200006b8        -       0x00000004   Zero   RW         1799    .bss.tim_ic_val     tim_app.o
    0x200006bc        -       0x00000014   Zero   RW         1785    .bss.time           rtc_app.o
    0x200006d0        -       0x00000080   Zero   RW          115    .bss.uart_rx_dma_buffer  usart.o
    0x20000750        -       0x00000008   Zero   RW         1663    .bss.ucLed          led_app.o
    0x20000758        -       0x0000002c   Zero   RW         1684    .bss.usart_rb       uart_app.o
    0x20000784        -       0x00000040   Zero   RW         1685    .bss.usart_read_buffer  uart_app.o
    0x200007c4        -       0x00000004   Zero   RW          372    .bss.uwTick         stm32g4xx_hal.o
    0x200007c8        -       0x00000400   Zero   RW            1    STACK               startup_stm32g431xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       732          0          0          0        412       7753   adc.o
       116          8          0          0        248        780   adc_app.o
       118          0          0          0          0       3435   dma.o
        52          0          0          0          0       2098   filter.o
       332          0          0          0          0       2514   gpio.o
       188          0          0          0         68       2695   key_app.o
      8768          0          0          2          4      26284   lcd.o
         2          0          0          0          0       1045   lcd_app.o
       102          0          0          1          8       1576   led_app.o
       316          0          0          0          0       3265   main.o
       222          0          0          0          0       2177   ringbuffer.o
       250          0          0          0         40       4354   rtc.o
        46          0          0          0         24       2422   rtc_app.o
        88          0          0         84          1       1185   scheduler.o
        36          8        472          0       1024        828   startup_stm32g431xx.o
       198          0          0          8          4       8547   stm32g4xx_hal.o
      1824          0          0          0          0      24880   stm32g4xx_hal_adc.o
       194          0          0          0          0      21839   stm32g4xx_hal_adc_ex.o
       196          0          0          0          0      10138   stm32g4xx_hal_cortex.o
       908          0          0          0          0       8830   stm32g4xx_hal_dma.o
       446          0          0          0          0       5225   stm32g4xx_hal_gpio.o
        52          0          0          0          0       1741   stm32g4xx_hal_msp.o
       260          0          0          0          0       9465   stm32g4xx_hal_pwr_ex.o
      2170          0          0          0          0       9904   stm32g4xx_hal_rcc.o
       692          0          0          0          0      10616   stm32g4xx_hal_rcc_ex.o
      1322          0          0          0          0      16191   stm32g4xx_hal_rtc.o
      3848         28          0          0          0      58950   stm32g4xx_hal_tim.o
       370          0          0          0          0      28811   stm32g4xx_hal_tim_ex.o
      2714         46         40          0          0      28298   stm32g4xx_hal_uart.o
       436          0         16          0          0      10323   stm32g4xx_hal_uart_ex.o
       120          0          0          0          0       3663   stm32g4xx_it.o
        48          0          0          0          0        881   system.o
        18          0         24          4          0       2756   system_stm32g4xx.o
      1204          0          0          0        324      10382   tim.o
        96          4          0          0        264       2130   tim_app.o
       140         24          0          0        108       4403   uart_app.o
       410          0          0          0        368       8835   usart.o

    ----------------------------------------------------------------------
     29220        <USER>        <GROUP>        104       2908     349219   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       186          0          2          5         11          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2258         90          0          0          0        452   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3702        <USER>          <GROUP>          4          0       1660   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2632        108          0          4          0       1004   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3702        <USER>          <GROUP>          4          0       1660   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     32922        226        586        108       2908     349551   Grand Totals
     32922        226        586        108       2908     349551   ELF Image Totals
     32922        226        586        108          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                33508 (  32.72kB)
    Total RW  Size (RW Data + ZI Data)              3016 (   2.95kB)
    Total ROM Size (Code + RO Data + RW Data)      33616 (  32.83kB)

==============================================================================

