{"name": "STM32_Xifeng", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32g431xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/adc.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/rtc.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32g4xx_it.c"}, {"path": "../Core/Src/stm32g4xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32G4xx_HAL_Driver", "files": [{"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_ll_adc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rtc_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart.c"}, {"path": "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_uart_ex.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32g4xx.c"}], "folders": []}]}, {"name": "MyAPP", "files": [{"path": "../APP/scheduler.c"}, {"path": "../APP/key_app.c"}, {"path": "../APP/lcd.c"}, {"path": "../APP/lcd_app.c"}, {"path": "../APP/led_app.c"}, {"path": "../APP/system.c"}, {"path": "../APP/uart_app.c"}, {"path": "../APP/ringbuffer.c"}, {"path": "../APP/adc_app.c"}, {"path": "../APP/filter.c"}, {"path": "../APP/i2c_hal.c"}, {"path": "../APP/rtc_app.c"}, {"path": "../APP/tim_app.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": ".pack/Keil/STM32G4xx_DFP_Revised.1.1.0", "miscInfo": {"uid": "2d846e05e18bf00371d2bf63a3844ba8"}, "targets": {"STM32_Xifeng": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x8000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x20000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32g4x", "interface": "cmsis-dap-v1", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "OpenOCD": {"bin": "", "target": "stm32g4x", "interface": "stlink", "baseAddr": "0x08000000"}, "STLink": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/stm32_xifeng.st.option.bytes.ini", "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "../Core/Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc", "../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32G4xx/Include", "../Drivers/CMSIS/Include", "../APP"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32G431xx"]}}}, "version": "3.4"}