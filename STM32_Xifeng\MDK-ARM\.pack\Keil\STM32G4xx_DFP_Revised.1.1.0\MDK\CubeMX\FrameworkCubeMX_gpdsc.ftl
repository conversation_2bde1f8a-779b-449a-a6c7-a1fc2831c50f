[#ftl]
[#list configs as dt]
[#assign data = dt]
[#assign usedIPs = dt.usedIPs]
[#assign peripheralParams = dt.peripheralParams]
[#assign peripheralGPIOParams = dt.peripheralGPIOParams]
[#assign peripheralDMAParams = dt.peripheralDMAParams]
[#assign peripheralNVICParams = dt.peripheralNVICParams]
[#assign deviceFamily="${dt.family}xx"]
[#assign tmpName="${dt.device?lower_case}.s"]
[#if dt.device?starts_with("STM32GBK1CB") == true]
[#assign startupFile = "startup_stm32gbk1cb.s"]
[#else]
[#assign startupFile = "startup_stm32g4${tmpName[7]}${tmpName[8]}xx.s"]
[/#if]
[#assign ll_used = 0]
[#assign hal_used = 0]
[#list usedIPs as ip]
[#if peripheralParams.get(ip)?? && peripheralParams.get(ip).get("driver") == ("LL")]
  [#assign ll_used = 1]
[/#if]
[#if ip?starts_with("NVIC") == false]
[#if peripheralParams.get(ip)?? && peripheralParams.get(ip).get("driver") == ("HAL")]
  [#assign hal_used = 1]
[/#if]
[/#if]
[/#list]
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- ******************************************************************************
 * File Name   : FrameworkCubeMX.gpdsc
 * Date        : ${date}
 * Description : Generator PDSC File generated by STM32CubeMX (DO NOT EDIT!)
 ****************************************************************************** -->
<package xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>Keil</vendor>
  <name>FrameworkCubeMX</name>
  <description>STM32CubeMX generated pack description</description>
  <url>project-path</url>
  <releases>
    <release version="1.0.0">
     - Generated: ${date}
    </release>
  </releases>
  <generators>
    <generator id="STM32CubeMX" Gvendor="STMicroelectronics" Gtool="STM32CubeMX" Gversion="4.10.0">
      <description>STM32CubeMX Environment</description>
      <select Dname="${(dt.device)}" Dvendor="STMicroelectronics:13"/>
      <command>$SMDK\CubeMX\STM32CubeMXLauncher</command>
      <workingDir>$PRTE\Device\${(dt.device)}</workingDir>
      <project_files>
        <file category="include" name="STCubeGenerated/Inc/"/>
        <file category="source" name="STCubeGenerated/Src/main.c" />
        <file category="header" name="STCubeGenerated/Inc/${deviceFamily?lower_case}_it.h"/>
        <file category="source" name="STCubeGenerated/Src/${deviceFamily?lower_case}_it.c"/>
      </project_files>
    </generator>
  </generators>
  <taxonomy>
    <description Cclass="Device" Cgroup="STM32Cube Framework" generator="STM32CubeMX">STM32Cube Framework</description>
  </taxonomy>
  <conditions>
    <condition id="STCubeMX">
      <description>Condition to include CMSIS core, Device Startup and HAL Drivers components</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32G4*"/>
      <require Cclass="CMSIS"  Cgroup="CORE"/>
      <require Cclass="Device" Cgroup="Startup"/>
      <require Cclass="Device" Cgroup="STM32Cube HAL"/>
    </condition>
  </conditions>
  <components>
    <bundle Cbundle="STM32CubeMX" Cclass="Device" Cversion="1.0.0">
      <component generator="STM32CubeMX" Cvendor="Keil" Cgroup="STM32Cube Framework" Csub="STM32CubeMX" Cversion="1.1.0" condition="STCubeMX">
        <description>Configuration via STM32CubeMX</description>
        <RTE_Components_h>
[#if ll_used == 1]
          #define RTE_DEVICE_LL_DRIVERS_USED
[/#if]
          #define RTE_DEVICE_FRAMEWORK_CUBE_MX
        </RTE_Components_h>
        <files>
          <file category="header" name="MX_Device.h"/>
          <file category="header" name="STCubeGenerated/Inc/${deviceFamily?lower_case}_hal_conf.h"/>
[#if hal_used == 1]
          <file category="source" name="STCubeGenerated/Src/${deviceFamily?lower_case}_hal_msp.c"/>
[/#if]
        </files>
      </component>
      <component  Cgroup="Startup">
        <description>System Startup for STMicroelectronics</description>
        <files>
          <file category="source" name="STCubeGenerated/MDK-ARM/${startupFile}" />
          <file category="source" name="STCubeGenerated/Src/system_${deviceFamily?lower_case}.c" />
        </files>
      </component>
    </bundle>
  </components>
</package>
[/#list]
