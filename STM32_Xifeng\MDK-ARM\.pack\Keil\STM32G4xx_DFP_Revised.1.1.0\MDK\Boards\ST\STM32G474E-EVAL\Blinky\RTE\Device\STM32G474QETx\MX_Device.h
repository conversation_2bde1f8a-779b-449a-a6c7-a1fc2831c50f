/******************************************************************************
 * File Name   : MX_Device.h
 * Date        : 15/05/2019 12:10:46
 * Description : STM32Cube MX parameter definitions
 * Note        : This file is generated by STM32CubeMX (DO NOT EDIT!)
 ******************************************************************************/

#ifndef __MX_DEVICE_H
#define __MX_DEVICE_H

/*---------------------------- Clock Configuration ---------------------------*/

#define MX_LSI_VALUE                            32000
#define MX_LSE_VALUE                            32768
#define MX_HSI_VALUE                            16000000
#define MX_HSE_VALUE                            24000000
#define MX_EXTERNAL_CLOCK_VALUE                 12288000
#define MX_SYSCLKFreq_VALUE                     170000000
#define MX_HCLKFreq_Value                       170000000
#define MX_FCLKCortexFreq_Value                 170000000
#define MX_CortexFreq_Value                     170000000
#define MX_AHBFreq_Value                        170000000
#define MX_APB1Freq_Value                       170000000
#define MX_APB2Freq_Value                       170000000
#define MX_APB1TimFreq_Value                    170000000
#define MX_APB2TimFreq_Value                    170000000
#define MX_ADC12Freq_Value                      170000000
#define MX_LPUART1Freq_Value                    170000000
#define MX_I2C1Freq_Value                       170000000
#define MX_I2SFreq_Value                        170000000
#define MX_USART1Freq_Value                     170000000
#define MX_USART2Freq_Value                     170000000
#define MX_USART3Freq_Value                     170000000
#define MX_FDCANFreq_Value                      170000000
#define MX_LPTIM1Freq_Value                     170000000
#define MX_QSPIFreq_Value                       170000000
#define MX_SAI1Freq_Value                       170000000
#define MX_PWRFreq_Value                        170000000
#define MX_RTCFreq_Value                        32000
#define MX_USBFreq_Value                        48000000
#define MX_WatchDogFreq_Value                   32000
#define MX_CECFreq_Value                        4000000
#define MX_HRTIM1Freq_Value                     170000000
#define MX_MCO1PinFreq_Value                    16000000

/*-------------------------------- ADC1       --------------------------------*/

#define MX_ADC1                                 1

/* GPIO Configuration */

/* Pin PA1 */
#define MX_ADC1_IN2_Pin                         PA1
#define MX_ADC1_IN2_GPIOx                       GPIOA
#define MX_ADC1_IN2_GPIO_PuPd                   GPIO_NOPULL
#define MX_ADC_OUTN                             ADC1_IN2
#define MX_ADC1_IN2_GPIO_Pin                    GPIO_PIN_1
#define MX_ADC1_IN2_GPIO_Mode                   GPIO_MODE_ANALOG

/*-------------------------------- ADC2       --------------------------------*/

#define MX_ADC2                                 1

/* GPIO Configuration */

/* Pin PC0 */
#define MX_ADC2_IN6_Pin                         PC0
#define MX_ADC2_IN6_GPIOx                       GPIOC
#define MX_ADC2_IN6_GPIO_PuPd                   GPIO_NOPULL
#define MX_VSENSE                               ADC2_IN6
#define MX_ADC2_IN6_GPIO_Pin                    GPIO_PIN_0
#define MX_ADC2_IN6_GPIO_Mode                   GPIO_MODE_ANALOG

/*-------------------------------- COMP6      --------------------------------*/

#define MX_COMP6                                1

/* GPIO Configuration */

/* Pin PC6 */
#define MX_COMP6_OUT_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_COMP6_OUT_Pin                        PC6
#define MX_COMP6_OUT_GPIOx                      GPIOC
#define MX_COMP6_OUT_GPIO_PuPd                  GPIO_NOPULL
#define MX_CMOP6_OUT                            COMP6_OUT
#define MX_COMP6_OUT_GPIO_Pin                   GPIO_PIN_6
#define MX_COMP6_OUT_GPIO_AF                    GPIO_AF7_COMP6
#define MX_COMP6_OUT_GPIO_Mode                  GPIO_MODE_AF_PP

/* Pin PB11 */
#define MX_COMP6_INP_Pin                        PB11
#define MX_COMP6_INP_GPIOx                      GPIOB
#define MX_COMP6_INP_GPIO_PuPd                  GPIO_NOPULL
#define MX_OPAMP4_COMP6_INP                     COMP6_INP
#define MX_COMP6_INP_GPIO_Pin                   GPIO_PIN_11
#define MX_COMP6_INP_GPIO_Mode                  GPIO_MODE_ANALOG

/*-------------------------------- DAC1       --------------------------------*/

#define MX_DAC1                                 1

/* GPIO Configuration */

/* Pin PA5 */
#define MX_COMP_DAC12_group_Pin                 PA5
#define MX_COMP_DAC12_group_GPIOx               GPIOA
#define MX_COMP_DAC12_group_GPIO_PuPd           GPIO_NOPULL
#define MX_MC_DAC_OUT2                          COMP_DAC12_group
#define MX_COMP_DAC12_group_GPIO_Pin            GPIO_PIN_5
#define MX_COMP_DAC12_group_GPIO_Mode           GPIO_MODE_ANALOG

/* Pin PA4 */
#define MX_COMP_DAC11_group_Pin                 PA4
#define MX_COMP_DAC11_group_GPIOx               GPIOA
#define MX_COMP_DAC11_group_GPIO_PuPd           GPIO_NOPULL
#define MX_DAC                                  COMP_DAC11_group
#define MX_COMP_DAC11_group_GPIO_Pin            GPIO_PIN_4
#define MX_COMP_DAC11_group_GPIO_Mode           GPIO_MODE_ANALOG

/*-------------------------------- FDCAN1     --------------------------------*/

#define MX_FDCAN1                               1

/* GPIO Configuration */

/* Pin PB9 */
#define MX_FDCAN1_TX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_FDCAN1_TX_Pin                        PB9
#define MX_FDCAN1_TX_GPIOx                      GPIOB
#define MX_FDCAN1_TX_GPIO_PuPd                  GPIO_NOPULL
#define MX_FDCAN1_TX                            FDCAN1_TX
#define MX_FDCAN1_TX_GPIO_Pin                   GPIO_PIN_9
#define MX_FDCAN1_TX_GPIO_AF                    GPIO_AF9_FDCAN1
#define MX_FDCAN1_TX_GPIO_Mode                  GPIO_MODE_AF_PP
#define MX_FDCAN1_TX_GPIO_FM9                   __NULL

/* Pin PB8-BOOT0 */
#define MX_FDCAN1_RX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_FDCAN1_RX_Pin                        PB8_BOOT0
#define MX_FDCAN1_RX_GPIOx                      GPIOB
#define MX_FDCAN1_RX_GPIO_PuPd                  GPIO_NOPULL
#define MX_BOOT0_FDCAN1_RX                      FDCAN1_RX
#define MX_FDCAN1_RX_GPIO_Pin                   GPIO_PIN_8
#define MX_FDCAN1_RX_GPIO_FM8                   __NULL
#define MX_FDCAN1_RX_GPIO_AF                    GPIO_AF9_FDCAN1
#define MX_FDCAN1_RX_GPIO_Mode                  GPIO_MODE_AF_PP

/*-------------------------------- FDCAN2     --------------------------------*/

#define MX_FDCAN2                               1

/* GPIO Configuration */

/* Pin PB5 */
#define MX_FDCAN2_RX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_FDCAN2_RX_Pin                        PB5
#define MX_FDCAN2_RX_GPIOx                      GPIOB
#define MX_FDCAN2_RX_GPIO_PuPd                  GPIO_NOPULL
#define MX_FDCAN2_RX                            FDCAN2_RX
#define MX_FDCAN2_RX_GPIO_Pin                   GPIO_PIN_5
#define MX_FDCAN2_RX_GPIO_AF                    GPIO_AF9_FDCAN2
#define MX_FDCAN2_RX_GPIO_Mode                  GPIO_MODE_AF_PP

/* Pin PB13 */
#define MX_FDCAN2_TX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_FDCAN2_TX_Pin                        PB13
#define MX_FDCAN2_TX_GPIOx                      GPIOB
#define MX_FDCAN2_TX_GPIO_PuPd                  GPIO_NOPULL
#define MX_FDCAN2_TX_PD_CC1                     FDCAN2_TX
#define MX_FDCAN2_TX_GPIO_Pin                   GPIO_PIN_13
#define MX_FDCAN2_TX_GPIO_AF                    GPIO_AF9_FDCAN2
#define MX_FDCAN2_TX_GPIO_Mode                  GPIO_MODE_AF_PP

/*-------------------------------- FMC        --------------------------------*/

#define MX_FMC                                  1

/* GPIO Configuration */

/* Pin PE11 */
#define MX_FMC_D8_DA8_Pin                       PE11
#define MX_FMC_D8_DA8_GPIOx                     GPIOE
#define MX_FMC_D8_DA8_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D8_DA8_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D8                               FMC_D8_DA8
#define MX_FMC_D8_DA8_GPIO_Pin                  GPIO_PIN_11
#define MX_FMC_D8_DA8_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D8_DA8_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PF10 */
#define MX_FMC_A0_Pin                           PF10
#define MX_FMC_A0_GPIOx                         GPIOF
#define MX_FMC_A0_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A0_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A0                               FMC_A0
#define MX_FMC_A0_GPIO_Pin                      GPIO_PIN_10
#define MX_FMC_A0_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A0_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PD7 */
#define MX_FMC_NE1_Pin                          PD7
#define MX_FMC_NE1_GPIOx                        GPIOD
#define MX_FMC_NE1_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_NE1_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_NE1                              FMC_NE1
#define MX_FMC_NE1_GPIO_Pin                     GPIO_PIN_7
#define MX_FMC_NE1_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_NE1_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PF7 */
#define MX_FMC_A1_Pin                           PF7
#define MX_FMC_A1_GPIOx                         GPIOF
#define MX_FMC_A1_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A1_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A1                               FMC_A1
#define MX_FMC_A1_GPIO_Pin                      GPIO_PIN_7
#define MX_FMC_A1_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A1_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PF2 */
#define MX_FMC_A2_Pin                           PF2
#define MX_FMC_A2_GPIOx                         GPIOF
#define MX_FMC_A2_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A2_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A2                               FMC_A2
#define MX_FMC_A2_GPIO_Pin                      GPIO_PIN_2
#define MX_FMC_A2_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A2_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PD1 */
#define MX_FMC_D3_DA3_Pin                       PD1
#define MX_FMC_D3_DA3_GPIOx                     GPIOD
#define MX_FMC_D3_DA3_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D3_DA3_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D3                               FMC_D3_DA3
#define MX_FMC_D3_DA3_GPIO_Pin                  GPIO_PIN_1
#define MX_FMC_D3_DA3_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D3_DA3_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PF3 */
#define MX_FMC_A3_Pin                           PF3
#define MX_FMC_A3_GPIOx                         GPIOF
#define MX_FMC_A3_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A3_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A3                               FMC_A3
#define MX_FMC_A3_GPIO_Pin                      GPIO_PIN_3
#define MX_FMC_A3_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A3_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PF4 */
#define MX_FMC_A4_Pin                           PF4
#define MX_FMC_A4_GPIOx                         GPIOF
#define MX_FMC_A4_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A4_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A4                               FMC_A4
#define MX_FMC_A4_GPIO_Pin                      GPIO_PIN_4
#define MX_FMC_A4_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A4_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PF5 */
#define MX_FMC_A5_Pin                           PF5
#define MX_FMC_A5_GPIOx                         GPIOF
#define MX_FMC_A5_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A5_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A5                               FMC_A5
#define MX_FMC_A5_GPIO_Pin                      GPIO_PIN_5
#define MX_FMC_A5_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A5_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PF12 */
#define MX_FMC_A6_Pin                           PF12
#define MX_FMC_A6_GPIOx                         GPIOF
#define MX_FMC_A6_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A6_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A6                               FMC_A6
#define MX_FMC_A6_GPIO_Pin                      GPIO_PIN_12
#define MX_FMC_A6_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A6_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PG0 */
#define MX_FMC_A10_Pin                          PG0
#define MX_FMC_A10_GPIOx                        GPIOG
#define MX_FMC_A10_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A10_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A10                              FMC_A10
#define MX_FMC_A10_GPIO_Pin                     GPIO_PIN_0
#define MX_FMC_A10_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A10_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PF13 */
#define MX_FMC_A7_Pin                           PF13
#define MX_FMC_A7_GPIOx                         GPIOF
#define MX_FMC_A7_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A7_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A7                               FMC_A7
#define MX_FMC_A7_GPIO_Pin                      GPIO_PIN_13
#define MX_FMC_A7_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A7_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PD9 */
#define MX_FMC_D14_DA14_Pin                     PD9
#define MX_FMC_D14_DA14_GPIOx                   GPIOD
#define MX_FMC_D14_DA14_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D14_DA14_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D14                              FMC_D14_DA14
#define MX_FMC_D14_DA14_GPIO_Pin                GPIO_PIN_9
#define MX_FMC_D14_DA14_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D14_DA14_GPIO_Mode               GPIO_MODE_AF_PP

/* Pin PF14 */
#define MX_FMC_A8_Pin                           PF14
#define MX_FMC_A8_GPIOx                         GPIOF
#define MX_FMC_A8_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A8_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A8                               FMC_A8
#define MX_FMC_A8_GPIO_Pin                      GPIO_PIN_14
#define MX_FMC_A8_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A8_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PG2 */
#define MX_FMC_A12_Pin                          PG2
#define MX_FMC_A12_GPIOx                        GPIOG
#define MX_FMC_A12_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A12_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A12                              FMC_A12
#define MX_FMC_A12_GPIO_Pin                     GPIO_PIN_2
#define MX_FMC_A12_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A12_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PF15 */
#define MX_FMC_A9_Pin                           PF15
#define MX_FMC_A9_GPIOx                         GPIOF
#define MX_FMC_A9_GPIO_PuPd                     GPIO_NOPULL
#define MX_FMC_A9_GPIO_Speed_High_Default       GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A9                               FMC_A9
#define MX_FMC_A9_GPIO_Pin                      GPIO_PIN_15
#define MX_FMC_A9_GPIO_AF                       GPIO_AF12_FMC
#define MX_FMC_A9_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PG1 */
#define MX_FMC_A11_Pin                          PG1
#define MX_FMC_A11_GPIOx                        GPIOG
#define MX_FMC_A11_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A11_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A11                              FMC_A11
#define MX_FMC_A11_GPIO_Pin                     GPIO_PIN_1
#define MX_FMC_A11_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A11_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PD5 */
#define MX_FMC_NWE_Pin                          PD5
#define MX_FMC_NWE_GPIOx                        GPIOD
#define MX_FMC_NWE_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_NWE_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_NWE                              FMC_NWE
#define MX_FMC_NWE_GPIO_Pin                     GPIO_PIN_5
#define MX_FMC_NWE_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_NWE_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PE14 */
#define MX_FMC_D11_DA11_Pin                     PE14
#define MX_FMC_D11_DA11_GPIOx                   GPIOE
#define MX_FMC_D11_DA11_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D11_DA11_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D11                              FMC_D11_DA11
#define MX_FMC_D11_DA11_GPIO_Pin                GPIO_PIN_14
#define MX_FMC_D11_DA11_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D11_DA11_GPIO_Mode               GPIO_MODE_AF_PP

/* Pin PE7 */
#define MX_FMC_D4_DA4_Pin                       PE7
#define MX_FMC_D4_DA4_GPIOx                     GPIOE
#define MX_FMC_D4_DA4_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D4_DA4_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D4                               FMC_D4_DA4
#define MX_FMC_D4_DA4_GPIO_Pin                  GPIO_PIN_7
#define MX_FMC_D4_DA4_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D4_DA4_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PD11 */
#define MX_FMC_A16_CLE_Pin                      PD11
#define MX_FMC_A16_CLE_GPIOx                    GPIOD
#define MX_FMC_A16_CLE_GPIO_PuPd                GPIO_NOPULL
#define MX_FMC_A16_CLE_GPIO_Speed_High_Default  GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A16                              FMC_A16_CLE
#define MX_FMC_A16_CLE_GPIO_Pin                 GPIO_PIN_11
#define MX_FMC_A16_CLE_GPIO_AF                  GPIO_AF12_FMC
#define MX_FMC_A16_CLE_GPIO_Mode                GPIO_MODE_AF_PP

/* Pin PE10 */
#define MX_FMC_D7_DA7_Pin                       PE10
#define MX_FMC_D7_DA7_GPIOx                     GPIOE
#define MX_FMC_D7_DA7_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D7_DA7_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D7                               FMC_D7_DA7
#define MX_FMC_D7_DA7_GPIO_Pin                  GPIO_PIN_10
#define MX_FMC_D7_DA7_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D7_DA7_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PE1 */
#define MX_FMC_NBL1_Pin                         PE1
#define MX_FMC_NBL1_GPIOx                       GPIOE
#define MX_FMC_NBL1_GPIO_PuPd                   GPIO_NOPULL
#define MX_FMC_NBL1_GPIO_Speed_High_Default     GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_NBL1                             FMC_NBL1
#define MX_FMC_NBL1_GPIO_Pin                    GPIO_PIN_1
#define MX_FMC_NBL1_GPIO_AF                     GPIO_AF12_FMC
#define MX_FMC_NBL1_GPIO_Mode                   GPIO_MODE_AF_PP

/* Pin PE0 */
#define MX_FMC_NBL0_Pin                         PE0
#define MX_FMC_NBL0_GPIOx                       GPIOE
#define MX_FMC_NBL0_GPIO_PuPd                   GPIO_NOPULL
#define MX_FMC_NBL0_GPIO_Speed_High_Default     GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_NBL0                             FMC_NBL0
#define MX_FMC_NBL0_GPIO_Pin                    GPIO_PIN_0
#define MX_FMC_NBL0_GPIO_AF                     GPIO_AF12_FMC
#define MX_FMC_NBL0_GPIO_Mode                   GPIO_MODE_AF_PP

/* Pin PD14 */
#define MX_FMC_D0_DA0_Pin                       PD14
#define MX_FMC_D0_DA0_GPIOx                     GPIOD
#define MX_FMC_D0_DA0_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D0_DA0_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D0                               FMC_D0_DA0
#define MX_FMC_D0_DA0_GPIO_Pin                  GPIO_PIN_14
#define MX_FMC_D0_DA0_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D0_DA0_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PD12 */
#define MX_FMC_A17_ALE_Pin                      PD12
#define MX_FMC_A17_ALE_GPIOx                    GPIOD
#define MX_FMC_A17_ALE_GPIO_PuPd                GPIO_NOPULL
#define MX_FMC_A17_ALE_GPIO_Speed_High_Default  GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A17                              FMC_A17_ALE
#define MX_FMC_A17_ALE_GPIO_Pin                 GPIO_PIN_12
#define MX_FMC_A17_ALE_GPIO_AF                  GPIO_AF12_FMC
#define MX_FMC_A17_ALE_GPIO_Mode                GPIO_MODE_AF_PP

/* Pin PE8 */
#define MX_FMC_D5_DA5_Pin                       PE8
#define MX_FMC_D5_DA5_GPIOx                     GPIOE
#define MX_FMC_D5_DA5_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D5_DA5_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D5                               FMC_D5_DA5
#define MX_FMC_D5_DA5_GPIO_Pin                  GPIO_PIN_8
#define MX_FMC_D5_DA5_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D5_DA5_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PE13 */
#define MX_FMC_D10_DA10_Pin                     PE13
#define MX_FMC_D10_DA10_GPIOx                   GPIOE
#define MX_FMC_D10_DA10_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D10_DA10_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D10                              FMC_D10_DA10
#define MX_FMC_D10_DA10_GPIO_Pin                GPIO_PIN_13
#define MX_FMC_D10_DA10_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D10_DA10_GPIO_Mode               GPIO_MODE_AF_PP

/* Pin PD8 */
#define MX_FMC_D13_DA13_Pin                     PD8
#define MX_FMC_D13_DA13_GPIOx                   GPIOD
#define MX_FMC_D13_DA13_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D13_DA13_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D13                              FMC_D13_DA13
#define MX_FMC_D13_DA13_GPIO_Pin                GPIO_PIN_8
#define MX_FMC_D13_DA13_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D13_DA13_GPIO_Mode               GPIO_MODE_AF_PP

/* Pin PD13 */
#define MX_FMC_A18_Pin                          PD13
#define MX_FMC_A18_GPIOx                        GPIOD
#define MX_FMC_A18_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A18_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A18                              FMC_A18
#define MX_FMC_A18_GPIO_Pin                     GPIO_PIN_13
#define MX_FMC_A18_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A18_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PE15 */
#define MX_FMC_D12_DA12_Pin                     PE15
#define MX_FMC_D12_DA12_GPIOx                   GPIOE
#define MX_FMC_D12_DA12_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D12_DA12_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D12                              FMC_D12_DA12
#define MX_FMC_D12_DA12_GPIO_Pin                GPIO_PIN_15
#define MX_FMC_D12_DA12_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D12_DA12_GPIO_Mode               GPIO_MODE_AF_PP

/* Pin PD15 */
#define MX_FMC_D1_DA1_Pin                       PD15
#define MX_FMC_D1_DA1_GPIOx                     GPIOD
#define MX_FMC_D1_DA1_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D1_DA1_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D1                               FMC_D1_DA1
#define MX_FMC_D1_DA1_GPIO_Pin                  GPIO_PIN_15
#define MX_FMC_D1_DA1_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D1_DA1_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PG4 */
#define MX_FMC_A14_Pin                          PG4
#define MX_FMC_A14_GPIOx                        GPIOG
#define MX_FMC_A14_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A14_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A14                              FMC_A14
#define MX_FMC_A14_GPIO_Pin                     GPIO_PIN_4
#define MX_FMC_A14_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A14_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PD4 */
#define MX_FMC_NOE_Pin                          PD4
#define MX_FMC_NOE_GPIOx                        GPIOD
#define MX_FMC_NOE_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_NOE_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_NOE                              FMC_NOE
#define MX_FMC_NOE_GPIO_Pin                     GPIO_PIN_4
#define MX_FMC_NOE_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_NOE_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PG3 */
#define MX_FMC_A13_Pin                          PG3
#define MX_FMC_A13_GPIOx                        GPIOG
#define MX_FMC_A13_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A13_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A13                              FMC_A13
#define MX_FMC_A13_GPIO_Pin                     GPIO_PIN_3
#define MX_FMC_A13_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A13_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PE12 */
#define MX_FMC_D9_DA9_Pin                       PE12
#define MX_FMC_D9_DA9_GPIOx                     GPIOE
#define MX_FMC_D9_DA9_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D9_DA9_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D9                               FMC_D9_DA9
#define MX_FMC_D9_DA9_GPIO_Pin                  GPIO_PIN_12
#define MX_FMC_D9_DA9_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D9_DA9_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PG5 */
#define MX_FMC_A15_Pin                          PG5
#define MX_FMC_A15_GPIOx                        GPIOG
#define MX_FMC_A15_GPIO_PuPd                    GPIO_NOPULL
#define MX_FMC_A15_GPIO_Speed_High_Default      GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_A15                              FMC_A15
#define MX_FMC_A15_GPIO_Pin                     GPIO_PIN_5
#define MX_FMC_A15_GPIO_AF                      GPIO_AF12_FMC
#define MX_FMC_A15_GPIO_Mode                    GPIO_MODE_AF_PP

/* Pin PE9 */
#define MX_FMC_D6_DA6_Pin                       PE9
#define MX_FMC_D6_DA6_GPIOx                     GPIOE
#define MX_FMC_D6_DA6_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D6_DA6_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D6                               FMC_D6_DA6
#define MX_FMC_D6_DA6_GPIO_Pin                  GPIO_PIN_9
#define MX_FMC_D6_DA6_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D6_DA6_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PD0 */
#define MX_FMC_D2_DA2_Pin                       PD0
#define MX_FMC_D2_DA2_GPIOx                     GPIOD
#define MX_FMC_D2_DA2_GPIO_PuPd                 GPIO_NOPULL
#define MX_FMC_D2_DA2_GPIO_Speed_High_Default   GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D2                               FMC_D2_DA2
#define MX_FMC_D2_DA2_GPIO_Pin                  GPIO_PIN_0
#define MX_FMC_D2_DA2_GPIO_AF                   GPIO_AF12_FMC
#define MX_FMC_D2_DA2_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PD10 */
#define MX_FMC_D15_DA15_Pin                     PD10
#define MX_FMC_D15_DA15_GPIOx                   GPIOD
#define MX_FMC_D15_DA15_GPIO_PuPd               GPIO_NOPULL
#define MX_FMC_D15_DA15_GPIO_Speed_High_Default GPIO_SPEED_FREQ_VERY_HIGH
#define MX_FMC_D15                              FMC_D15_DA15
#define MX_FMC_D15_DA15_GPIO_Pin                GPIO_PIN_10
#define MX_FMC_D15_DA15_GPIO_AF                 GPIO_AF12_FMC
#define MX_FMC_D15_DA15_GPIO_Mode               GPIO_MODE_AF_PP

/*-------------------------------- HRTIM1     --------------------------------*/

#define MX_HRTIM1                               1

/* GPIO Configuration */

/* Pin PC8 */
#define MX_HRTIM1_CHE1_Pin                      PC8
#define MX_HRTIM1_CHE1_GPIOx                    GPIOC
#define MX_HRTIM1_CHE1_GPIO_PuPd                GPIO_NOPULL
#define MX_HRTIM1_CHE1_GPIO_Speed_High_Default  GPIO_SPEED_FREQ_VERY_HIGH
#define MX_BK_Drive                             HRTIM1_CHE1
#define MX_HRTIM1_CHE1_GPIO_Pin                 GPIO_PIN_8
#define MX_HRTIM1_CHE1_GPIO_AF                  GPIO_AF3_HRTIM1
#define MX_HRTIM1_CHE1_GPIO_Mode                GPIO_MODE_AF_PP

/*-------------------------------- I2C3       --------------------------------*/

#define MX_I2C3                                 1

/* GPIO Configuration */

/* Pin PG6 */
#define MX_I2C3_SMBA_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_I2C3_SMBA_Pin                        PG6
#define MX_I2C3_SMBA_GPIOx                      GPIOG
#define MX_I2C3_SMBA_GPIO_PuPdOD                GPIO_PULLUP
#define MX_I2C3_SMBA                            I2C3_SMBA
#define MX_I2C3_SMBA_GPIO_Pin                   GPIO_PIN_6
#define MX_I2C3_SMBA_GPIO_AF                    GPIO_AF4_I2C3
#define MX_I2C3_SMBA_GPIO_Mode                  GPIO_MODE_AF_OD

/* Pin PG8 */
#define MX_I2C3_SDA_GPIO_Speed                  GPIO_SPEED_FREQ_LOW
#define MX_I2C3_SDA_Pin                         PG8
#define MX_I2C3_SDA_GPIOx                       GPIOG
#define MX_I2C3_SDA_GPIO_PuPdOD                 GPIO_PULLUP
#define MX_I2C3_SDA                             I2C3_SDA
#define MX_I2C3_SDA_GPIO_Pin                    GPIO_PIN_8
#define MX_I2C3_SDA_GPIO_AF                     GPIO_AF4_I2C3
#define MX_I2C3_SDA_GPIO_Mode                   GPIO_MODE_AF_OD

/* Pin PG7 */
#define MX_I2C3_SCL_GPIO_Speed                  GPIO_SPEED_FREQ_LOW
#define MX_I2C3_SCL_Pin                         PG7
#define MX_I2C3_SCL_GPIOx                       GPIOG
#define MX_I2C3_SCL_GPIO_PuPdOD                 GPIO_PULLUP
#define MX_I2C3_SCL                             I2C3_SCL
#define MX_I2C3_SCL_GPIO_Pin                    GPIO_PIN_7
#define MX_I2C3_SCL_GPIO_AF                     GPIO_AF4_I2C3
#define MX_I2C3_SCL_GPIO_Mode                   GPIO_MODE_AF_OD

/*-------------------------------- SAI1       --------------------------------*/

#define MX_SAI1                                 1

/* GPIO Configuration */

/* Pin PA8 */
#define MX_SAI1_SCK_A_GPIO_Speed                GPIO_SPEED_FREQ_LOW
#define MX_SAI1_SCK_A_Pin                       PA8
#define MX_SAI1_SCK_A_GPIOx                     GPIOA
#define MX_SAI1_SCK_A_GPIO_PuPd                 GPIO_NOPULL
#define MX_SAI_SCK_A                            SAI1_SCK_A
#define MX_SAI1_SCK_A_GPIO_Pin                  GPIO_PIN_8
#define MX_SAI1_SCK_A_GPIO_AF                   GPIO_AF14_SAI1
#define MX_SAI1_SCK_A_GPIO_Mode                 GPIO_MODE_AF_PP

/* Pin PE2 */
#define MX_SAI1_MCLK_A_GPIO_Speed               GPIO_SPEED_FREQ_LOW
#define MX_SAI1_MCLK_A_Pin                      PE2
#define MX_SAI1_MCLK_A_GPIOx                    GPIOE
#define MX_SAI1_MCLK_A_GPIO_PuPd                GPIO_NOPULL
#define MX_SAI_MCLK_A                           SAI1_MCLK_A
#define MX_SAI1_MCLK_A_GPIO_Pin                 GPIO_PIN_2
#define MX_SAI1_MCLK_A_GPIO_AF                  GPIO_AF13_SAI1
#define MX_SAI1_MCLK_A_GPIO_Mode                GPIO_MODE_AF_PP

/* Pin PE4 */
#define MX_SAI1_FS_A_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_SAI1_FS_A_Pin                        PE4
#define MX_SAI1_FS_A_GPIOx                      GPIOE
#define MX_SAI1_FS_A_GPIO_PuPd                  GPIO_NOPULL
#define MX_SAI_FS_A                             SAI1_FS_A
#define MX_SAI1_FS_A_GPIO_Pin                   GPIO_PIN_4
#define MX_SAI1_FS_A_GPIO_AF                    GPIO_AF13_SAI1
#define MX_SAI1_FS_A_GPIO_Mode                  GPIO_MODE_AF_PP

/* Pin PD6 */
#define MX_SAI1_SD_A_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_SAI1_SD_A_Pin                        PD6
#define MX_SAI1_SD_A_GPIOx                      GPIOD
#define MX_SAI1_SD_A_GPIO_PuPd                  GPIO_NOPULL
#define MX_SAI_SD_A                             SAI1_SD_A
#define MX_SAI1_SD_A_GPIO_Pin                   GPIO_PIN_6
#define MX_SAI1_SD_A_GPIO_AF                    GPIO_AF13_SAI1
#define MX_SAI1_SD_A_GPIO_Mode                  GPIO_MODE_AF_PP

/* Pin PE3 */
#define MX_SAI1_SD_B_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_SAI1_SD_B_Pin                        PE3
#define MX_SAI1_SD_B_GPIOx                      GPIOE
#define MX_SAI1_SD_B_GPIO_PuPd                  GPIO_NOPULL
#define MX_SAI_SD_B                             SAI1_SD_B
#define MX_SAI1_SD_B_GPIO_Pin                   GPIO_PIN_3
#define MX_SAI1_SD_B_GPIO_AF                    GPIO_AF13_SAI1
#define MX_SAI1_SD_B_GPIO_Mode                  GPIO_MODE_AF_PP

/*-------------------------------- SPI2       --------------------------------*/

#define MX_SPI2                                 1

/* GPIO Configuration */

/* Pin PF9 */
#define MX_SPI2_SCK_GPIO_Speed                  GPIO_SPEED_FREQ_LOW
#define MX_SPI2_SCK_Pin                         PF9
#define MX_SPI2_SCK_GPIOx                       GPIOF
#define MX_SPI2_SCK_GPIO_PuPd                   GPIO_NOPULL
#define MX_SPI2_SCK                             SPI2_SCK
#define MX_SPI2_SCK_GPIO_Pin                    GPIO_PIN_9
#define MX_SPI2_SCK_GPIO_AF                     GPIO_AF5_SPI2
#define MX_SPI2_SCK_GPIO_Mode                   GPIO_MODE_AF_PP

/* Pin PB14 */
#define MX_SPI2_MISO_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_SPI2_MISO_Pin                        PB14
#define MX_SPI2_MISO_GPIOx                      GPIOB
#define MX_SPI2_MISO_GPIO_PuPd                  GPIO_NOPULL
#define MX_SPI2_MISO                            SPI2_MISO
#define MX_SPI2_MISO_GPIO_Pin                   GPIO_PIN_14
#define MX_SPI2_MISO_GPIO_AF                    GPIO_AF5_SPI2
#define MX_SPI2_MISO_GPIO_Mode                  GPIO_MODE_AF_PP

/* Pin PB15 */
#define MX_SPI2_MOSI_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_SPI2_MOSI_Pin                        PB15
#define MX_SPI2_MOSI_GPIOx                      GPIOB
#define MX_SPI2_MOSI_GPIO_PuPd                  GPIO_NOPULL
#define MX_SPI2_MOSI                            SPI2_MOSI
#define MX_SPI2_MOSI_GPIO_Pin                   GPIO_PIN_15
#define MX_SPI2_MOSI_GPIO_AF                    GPIO_AF5_SPI2
#define MX_SPI2_MOSI_GPIO_Mode                  GPIO_MODE_AF_PP

/*-------------------------------- SYS        --------------------------------*/

#define MX_SYS                                  1

/* GPIO Configuration */

/* Pin PB4 */
#define MX_SYS_JTRST_Pin                        PB4
#define MX_JTRST_PD_CC2                         SYS_JTRST

/* Pin PA13 */
#define MX_SYS_JTMS_SWDIO_Pin                   PA13
#define MX_JTMS_SWDIO                           SYS_JTMS_SWDIO

/* Pin PA14 */
#define MX_SYS_JTCK_SWCLK_Pin                   PA14
#define MX_JTCK_SWCLK                           SYS_JTCK_SWCLK

/* Pin PE6 */
#define MX_SYS_TRACED3_Pin                      PE6
#define MX_TRACED3                              SYS_TRACED3

/* Pin PE5 */
#define MX_SYS_TRACED2_Pin                      PE5
#define MX_TRACED2                              SYS_TRACED2

/* Pin PA15 */
#define MX_SYS_JTDI_Pin                         PA15
#define MX_JTDI                                 SYS_JTDI

/* Pin PB3 */
#define MX_SYS_JTDO_SWO_Pin                     PB3
#define MX_JTDO_SWO                             SYS_JTDO_SWO

/*-------------------------------- TIM8       --------------------------------*/

#define MX_TIM8                                 1

/* GPIO Configuration */

/*-------------------------------- USART1     --------------------------------*/

#define MX_USART1                               1

#define MX_USART1_VM                            VM_ASYNC

/* GPIO Configuration */

/* Pin PA9 */
#define MX_USART1_TX_GPIO_ModeDefaultPP         GPIO_MODE_AF_PP
#define MX_USART1_TX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_USART1_TX_Pin                        PA9
#define MX_USART1_TX_GPIOx                      GPIOA
#define MX_USART1_TX_GPIO_PuPd                  GPIO_NOPULL
#define MX_USART1_TX_[STLink_VCP_RX]            USART1_TX
#define MX_USART1_TX_GPIO_Pin                   GPIO_PIN_9
#define MX_USART1_TX_GPIO_AF                    GPIO_AF7_USART1

/* Pin PA10 */
#define MX_USART1_RX_GPIO_ModeDefaultPP         GPIO_MODE_AF_PP
#define MX_USART1_RX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_USART1_RX_Pin                        PA10
#define MX_USART1_RX_GPIOx                      GPIOA
#define MX_USART1_RX_GPIO_PuPd                  GPIO_NOPULL
#define MX_USART1_RX_[STLink_VCP_TX]            USART1_RX
#define MX_USART1_RX_GPIO_Pin                   GPIO_PIN_10
#define MX_USART1_RX_GPIO_AF                    GPIO_AF7_USART1

/*-------------------------------- USART3     --------------------------------*/

#define MX_USART3                               1

#define MX_USART3_VM                            VM_SMARTCARD

/* GPIO Configuration */

/* Pin PC10 */
#define MX_USART3_TX_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_USART3_TX_Pin                        PC10
#define MX_USART3_TX_GPIOx                      GPIOC
#define MX_USART3_TX_GPIO_PuPd                  GPIO_NOPULL
#define MX_SmartCard_IO                         USART3_TX
#define MX_USART3_TX_GPIO_Pin                   GPIO_PIN_10
#define MX_USART3_TX_GPIO_ModeDefaultOD         GPIO_MODE_AF_OD
#define MX_USART3_TX_GPIO_AF                    GPIO_AF7_USART3

/* Pin PC12 */
#define MX_USART3_CK_GPIO_Speed                 GPIO_SPEED_FREQ_LOW
#define MX_USART3_CK_Pin                        PC12
#define MX_USART3_CK_GPIOx                      GPIOC
#define MX_USART3_CK_GPIO_PuPd                  GPIO_NOPULL
#define MX_SmartCard_CLK                        USART3_CK
#define MX_USART3_CK_GPIO_Pin                   GPIO_PIN_12
#define MX_USART3_CK_GPIO_AF                    GPIO_AF7_USART3
#define MX_USART3_CK_GPIO_Mode                  GPIO_MODE_AF_PP

/*-------------------------------- USB        --------------------------------*/

#define MX_USB                                  1

/* GPIO Configuration */

/* Pin PA11 */
#define MX_USB_DM_GPIO_Speed                    GPIO_SPEED_FREQ_LOW
#define MX_USB_DM_Pin                           PA11
#define MX_USB_DM_GPIOx                         GPIOA
#define MX_USB_DM_GPIO_PuPd                     GPIO_NOPULL
#define MX_USB_DM                               USB_DM
#define MX_USB_DM_GPIO_Pin                      GPIO_PIN_11
#define MX_USB_DM_GPIO_Mode                     GPIO_MODE_AF_PP

/* Pin PA12 */
#define MX_USB_DP_GPIO_Speed                    GPIO_SPEED_FREQ_LOW
#define MX_USB_DP_Pin                           PA12
#define MX_USB_DP_GPIOx                         GPIOA
#define MX_USB_DP_GPIO_PuPd                     GPIO_NOPULL
#define MX_USB_DP                               USB_DP
#define MX_USB_DP_GPIO_Pin                      GPIO_PIN_12
#define MX_USB_DP_GPIO_Mode                     GPIO_MODE_AF_PP

/*-------------------------------- NVIC       --------------------------------*/

#define MX_NVIC                                 1

/*-------------------------------- GPIO       --------------------------------*/

#define MX_GPIO                                 1

/* GPIO Configuration */

/* Pin PC13 */
#define MX_PC13_Pin                             PC13
#define MX_PC13_GPIOx                           GPIOC
#define MX_PC13_GPIO_PuPd                       GPIO_NOPULL
#define MX_TAMPER_KEY                           PC13
#define MX_PC13_GPIO_Pin                        GPIO_PIN_13
#define MX_PC13_GPIO_Mode                       GPIO_MODE_INPUT

/* Pin PB2 */
#define MX_PB2_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PB2_Pin                              PB2
#define MX_PB2_GPIOx                            GPIOB
#define MX_PB2_PinState                         GPIO_PIN_RESET
#define MX_PB2_GPIO_PuPd                        GPIO_NOPULL
#define MX_DISCHARGE                            PB2
#define MX_PB2_GPIO_Pin                         GPIO_PIN_2
#define MX_PB2_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

/* Pin PF6 */
#define MX_PF6_Pin                              PF6
#define MX_PF6_GPIOx                            GPIOF
#define MX_PF6_GPIO_PuPd                        GPIO_NOPULL
#define MX_Audio_INT                            PF6
#define MX_PF6_GPIO_Pin                         GPIO_PIN_6
#define MX_PF6_GPIO_ModeDefaultEXTI             GPIO_MODE_IT_RISING

/* Pin PF8 */
#define MX_PF8_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PF8_Pin                              PF8
#define MX_PF8_GPIOx                            GPIOF
#define MX_PF8_PinState                         GPIO_PIN_RESET
#define MX_PF8_GPIO_PuPd                        GPIO_NOPULL
#define MX_uSD_CS                               PF8
#define MX_PF8_GPIO_Pin                         GPIO_PIN_8
#define MX_PF8_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

/* Pin PF11 */
#define MX_PF11_GPIO_Speed                      GPIO_SPEED_FREQ_LOW
#define MX_PF11_Pin                             PF11
#define MX_PF11_GPIOx                           GPIOF
#define MX_PF11_PinState                        GPIO_PIN_RESET
#define MX_PF11_GPIO_PuPd                       GPIO_NOPULL
#define MX_LED3_[red]                           PF11
#define MX_PF11_GPIO_Pin                        GPIO_PIN_11
#define MX_PF11_GPIO_ModeDefaultOutputPP        GPIO_MODE_OUTPUT_PP

/* Pin PG9 */
#define MX_PG9_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PG9_Pin                              PG9
#define MX_PG9_GPIOx                            GPIOG
#define MX_PG9_PinState                         GPIO_PIN_RESET
#define MX_PG9_GPIO_PuPd                        GPIO_NOPULL
#define MX_LED1_[green]                         PG9
#define MX_PG9_GPIO_Pin                         GPIO_PIN_9
#define MX_PG9_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

/* Pin PC7 */
#define MX_PC7_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PC7_Pin                              PC7
#define MX_PC7_GPIOx                            GPIOC
#define MX_PC7_PinState                         GPIO_PIN_RESET
#define MX_PC7_GPIO_PuPd                        GPIO_NOPULL
#define MX_MFX_WAKEUP                           PC7
#define MX_PC7_GPIO_Pin                         GPIO_PIN_7
#define MX_PC7_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

/* Pin PC11 */
#define MX_PC11_GPIO_Speed                      GPIO_SPEED_FREQ_LOW
#define MX_PC11_Pin                             PC11
#define MX_PC11_GPIOx                           GPIOC
#define MX_PC11_PinState                        GPIO_PIN_RESET
#define MX_PC11_GPIO_PuPd                       GPIO_NOPULL
#define MX_SOURCE_EN                            PC11
#define MX_PC11_GPIO_Pin                        GPIO_PIN_11
#define MX_PC11_GPIO_ModeDefaultOutputPP        GPIO_MODE_OUTPUT_PP

/* Pin PC9 */
#define MX_PC9_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PC9_Pin                              PC9
#define MX_PC9_GPIOx                            GPIOC
#define MX_PC9_PinState                         GPIO_PIN_RESET
#define MX_PC9_GPIO_PuPd                        GPIO_NOPULL
#define MX_LCD_CS                               PC9
#define MX_PC9_GPIO_Pin                         GPIO_PIN_9
#define MX_PC9_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

/* Pin PA0 */
#define MX_PA0_Pin                              PA0
#define MX_PA0_GPIOx                            GPIOA
#define MX_PA0_GPIO_PuPd                        GPIO_NOPULL
#define MX_MFX_IRQ_OUT                          PA0
#define MX_PA0_GPIO_Pin                         GPIO_PIN_0
#define MX_PA0_GPIO_ModeDefaultEXTI             GPIO_MODE_IT_RISING

/* Pin PD2 */
#define MX_PD2_GPIO_Speed                       GPIO_SPEED_FREQ_LOW
#define MX_PD2_Pin                              PD2
#define MX_PD2_GPIOx                            GPIOD
#define MX_PD2_PinState                         GPIO_PIN_RESET
#define MX_PD2_GPIO_PuPd                        GPIO_NOPULL
#define MX_RS485_DIR                            PD2
#define MX_PD2_GPIO_Pin                         GPIO_PIN_2
#define MX_PD2_GPIO_ModeDefaultOutputPP         GPIO_MODE_OUTPUT_PP

#endif  /* __MX_DEVICE_H */

